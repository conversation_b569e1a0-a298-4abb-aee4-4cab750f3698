package net.summerfarm.tms.controller.delivery.vo.city;

/**
 * ApiModelPropert
 */

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> xiang
 * create at:  2021-11-12
 */
@Data
public class DeliveryExpenseVO {

    /**
     * id
     */
    @NotNull(message = "id.not.null")
    private Integer id;

    /**
     * 司机id
     */
    private Integer driverId;

    /**
     * 配送信息id
     */
    private Integer deliveryPathId;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 报销类型
     */
    private Integer type;

    /**
     * 城配仓
     */
    @NotNull(message = "storeNo.not.null")
    private Integer storeNo;

    /**
     * 是否复核
     */
    private Integer isReview;

    /**
     * 店铺id
     */
    private Integer mId;

    /**
     * 店铺名称
     */
    private String mname;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 审核状态
     */
    private Integer status;
    /**
     * 审核失败原因
     */
    private String reason;

}
