package net.summerfarm.tms.controller.delivery.vo;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.enums.DeliveryBatchStatusEnum;
import net.summerfarm.tms.enums.DeliveryBatchTypeEnum;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 配送批次转换器
 * date: 2022/12/7 15:21
 *
 * <AUTHOR>
 */
public class DeliveryBatchVOConverter {

    public static DeliveryBatchVO dto2Vo(DeliveryBatchDTO deliveryBatchDTO) {
        DeliveryBatchVO deliveryBatchVO = new DeliveryBatchVO();
        if (deliveryBatchDTO == null) {
            return deliveryBatchVO;
        }
        deliveryBatchVO.setBatchId(deliveryBatchDTO.getDeliveryBatchId());
        deliveryBatchVO.setStatus(deliveryBatchDTO.getStatus());
        deliveryBatchVO.setStatusDesc(DeliveryBatchStatusEnum.trunkMap.get(deliveryBatchDTO.getStatus()));
        deliveryBatchVO.setType(deliveryBatchDTO.getType());
        deliveryBatchVO.setTypeDesc(DeliveryBatchTypeEnum.map.get(deliveryBatchDTO.getType()));
        deliveryBatchVO.setCarrierId(deliveryBatchDTO.getCarrierId());
        deliveryBatchVO.setCarrierName(deliveryBatchDTO.getCarrierName());
        deliveryBatchVO.setDriverId(deliveryBatchDTO.getDriverId());
        deliveryBatchVO.setDriverName(deliveryBatchDTO.getDriver());
        deliveryBatchVO.setDriverPhone(deliveryBatchDTO.getDriverPhone());
        deliveryBatchVO.setCarId(deliveryBatchDTO.getCarId());
        deliveryBatchVO.setCarNum(deliveryBatchDTO.getCarNumber());
        deliveryBatchVO.setCarTypeDesc(deliveryBatchDTO.getCarType());
        deliveryBatchVO.setStorageName(deliveryBatchDTO.getStorageName());
        deliveryBatchVO.setDeliveryTime(deliveryBatchDTO.getDeliveryTime());
        deliveryBatchVO.setBeginTime(deliveryBatchDTO.getBeginTime());
        deliveryBatchVO.setCreatTime(deliveryBatchDTO.getCreatTime());
        deliveryBatchVO.setCreator(deliveryBatchDTO.getCreator());
        deliveryBatchVO.setPathName(deliveryBatchDTO.getPathName());
        deliveryBatchVO.setCloseReason(deliveryBatchDTO.getCloseReason());
        deliveryBatchVO.setEstimateFare(deliveryBatchDTO.getEstimateFare());

        deliveryBatchVO.setDeliverySiteList(deliveryBatchDTO.getDeliverySiteDTOList());
        deliveryBatchVO.setDeliveryOrderDTOList(deliveryBatchDTO.getDeliveryOrderDTOList());
        return deliveryBatchVO;
    }

    public static PageInfo<DeliveryBatchVO> dtoPage2Vo(TmsResult<PageInfo<DeliveryBatchDTO>> deliveryDTOPage) {
        PageInfo<DeliveryBatchDTO> deliveryDTOPageInfo = deliveryDTOPage.getData();
        List<DeliveryBatchDTO> list = deliveryDTOPageInfo.getList();

        List<DeliveryBatchVO> deliveryBatchVOList = list.stream().map(DeliveryBatchVOConverter::dto2Vo).collect(Collectors.toList());

        PageInfo<DeliveryBatchVO> listPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(deliveryDTOPageInfo, listPageInfo);
        listPageInfo.setList(deliveryBatchVOList);
        return listPageInfo;
    }
}
