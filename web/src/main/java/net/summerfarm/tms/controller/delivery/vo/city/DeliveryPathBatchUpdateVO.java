package net.summerfarm.tms.controller.delivery.vo.city;

import lombok.Data;
import net.summerfarm.tms.delivery.group.ValidationGroups;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/4/13 11:46<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryPathBatchUpdateVO {

    @NotNull(message = "ids.not.null",groups = ValidationGroups.BatchUpdateSiteRoute.class)
    private List<Integer> ids;

    @NotNull(message = "path.not.null",groups = ValidationGroups.BatchUpdateSiteRoute.class)
    private String path;

    private Integer sort;

    @NotNull(message = "storeNo.not.null",groups = ValidationGroups.BatchUpdateSiteRoute.class)
    private Integer storeNo;
}
