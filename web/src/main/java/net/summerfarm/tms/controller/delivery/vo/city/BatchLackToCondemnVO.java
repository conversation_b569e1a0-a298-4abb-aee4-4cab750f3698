package net.summerfarm.tms.controller.delivery.vo.city;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/5/31 10:40<br/>
 *
 * <AUTHOR> />
 */
@Data
public class BatchLackToCondemnVO implements Serializable {
    @NotNull(message = "ids不能为空")
    private List<Long> ids;

    /**
     * 责任方 1库存仓 2司机 3无法判责
     */
    @NotNull(message = "responsible不能为空")
    private Integer responsible;

    /**
     * 是否买赔 0是 1否
     */
    @NotNull(message = "buyOut不能为空")
    private Integer buyOut;

    /**
     * 仓库编号
     */
    @NotNull(message = "storeNo.not.null")
    private Integer storeNo;

    private BigDecimal buyOutMoney;

    private String judgmentOpinion;
}
