package net.summerfarm.tms.controller.delivery.vo.city;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2022/10/18 10:40<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseLogisticsCenterVO {
    private Integer id;
    @ApiModelProperty("配送仓编号")
    private Integer storeNo;
    @ApiModelProperty("配送仓名称")
    private String storeName;
    @ApiModelProperty("城市负责人")
    private Integer manageAdminId;
    @ApiModelProperty(" 配送中心状态：0、失效 1、有效")
    private Integer status;
    @ApiModelProperty("地址")
    private String address;
    @ApiModelProperty("高德地图poi坐标")
    private String poiNote;
    @ApiModelProperty("出库任务完成时间")
    private LocalDateTime sotFinishTime;
    @ApiModelProperty("是否支持提前截单：0、false 1、true")
    private Integer closeOrderType;
    @ApiModelProperty("同步库存使用仓编号")
    private Integer originStoreNo;
    @ApiModelProperty("修改人adminId")
    private Integer updater;
    private LocalDateTime updateTime;
    @ApiModelProperty("创建人adminId")
    private Integer creator;
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    private String closeTime;
    private String updateCloseTime;
    private Integer stopDeliveryStatus;
    private Integer punchState;
    private BigDecimal punchDistance;
    private String outTime;
    private String personContact;
    private String phone;
    /**
     * 0智能排线 1手动排线
     */
    private Integer intelligencePath;

}
