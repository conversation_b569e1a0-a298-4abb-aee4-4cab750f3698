package net.summerfarm.tms.controller.delivery.vo.city;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: <EMAIL>
 * @create: 2022/7/11
 */
@Data
public class NotifyDeliveryFeeDTO implements Serializable {

    /**
     * 联系人Id
     */
    private Long contactId;

    /**
     * 配送费
     */
    private BigDecimal deliveryFee;


    /**
     * 仓库编号
     */
    @NotNull(message = "storeNo.not.null")
    private Integer storeNo;
}
