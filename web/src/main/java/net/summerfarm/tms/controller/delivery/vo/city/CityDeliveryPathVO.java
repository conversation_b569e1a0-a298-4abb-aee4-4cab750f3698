package net.summerfarm.tms.controller.delivery.vo.city;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/10/18 10:30<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CityDeliveryPathVO {
    /**
     * 城配仓信息
     */
    private WarehouseLogisticsCenterVO areaInfo;

    /**
     * 是否第一次排线
     */
    private boolean isExist = false;

    /**
     * 路线最大距离
     */
    private PathMaxVO maxDistance;
    /**
     * 路线最大店铺数
     */
    private PathMaxVO maxNumber;
    /**
     * 路线最大货值
     */
    private PathMaxVO maxPrice;
    /**
     * 路线最大体积
     */
    private PathMaxVO maxVolume;
    /**
     * 是否完成排线 true表示完成 false没有完成
     */
    private boolean pathStatus;

    /**
     * 排线数据
     */
    private List<DeliveryPathSortVO> sort;

    /**
     * 排线数据
     */
    List<DeliveryPathVO> unSort;

    /**
     * 已排线数量
     */
    private int sortNumber;

    /**
     * 未排线数量
     */
    private int unSortNumber;

    /**
     * 总店铺数
     */
    private int totalNumber;

    /**
     * 计算总距离
     */
    private BigDecimal planTotalDistance;
    /**
     * 最优计算总距离
     */
    private BigDecimal intelligenceTotalDistance;

    /**
     * 总路线
     */
    private int totalPath;

    /**
     * 路线总价值  没有用了
     */
    private BigDecimal totalPrice;

    /**
     * 路线总体积
     */
    private BigDecimal totalVolume;

    /**
     * 平均路线店铺数
     */
    private BigDecimal average;

    /**
     * 路线总满载率
     */
    private BigDecimal fullLoadRatio;

    /**
     * 总重量
     */
    private BigDecimal totalWeight;



}
