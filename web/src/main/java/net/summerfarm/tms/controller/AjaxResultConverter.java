package net.summerfarm.tms.controller;

import com.alibaba.fastjson.JSON;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.common.AjaxResult;

/**
 * <AUTHOR>
 */
public class AjaxResultConverter {
    @SuppressWarnings("unchecked")
    public static <T> TmsResult<T> ajax2TmsResult(AjaxResult<T> ajaxResult) {
        TmsResult<T> tmsResult = new TmsResult<T>();
        tmsResult.setMsg(ajaxResult.getMsg());
        tmsResult.setData((T) ajaxResult.getData());
        tmsResult.setCode(ajaxResult.getCode());
        return tmsResult;
    }

    @SuppressWarnings("unchecked")
    public static <T> TmsResult<T> ajax2TmsResult(String result) {
        AjaxResult ajaxResult = JSON.parseObject(result, AjaxResult.class);
        return ajax2TmsResult(ajaxResult);
    }
}
