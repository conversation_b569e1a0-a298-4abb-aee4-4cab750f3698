package net.summerfarm.tms.schedulex;

import cn.hutool.core.date.DateUtil;
import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.enums.TmsSiteTypeEnum;
import net.summerfarm.tms.gray.dto.DataSyncCompareResult;
import net.summerfarm.tms.gray.dto.OldDistOrderDTO;
import net.summerfarm.tms.gray.old2new.Old2NewSyncService;
import net.summerfarm.tms.query.base.carrier.SiteQuery;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/11/24 20:56<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class DataCheakProcessor  extends XianMuJavaProcessorV2 {

    @Resource
    private Old2NewSyncService old2NewSyncService;
    @Resource
    private SiteRepository siteRepository;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        //获取全部城配仓的数据
        List<SiteEntity> siteEntities = siteRepository.queryList(SiteQuery.builder().type(TmsSiteTypeEnum.STORE.getCode()).build());
        List<String> storeNos = siteEntities.stream().map(SiteEntity::getOutBusinessNo).collect(Collectors.toList());
        String today = DateUtil.today();

        StringJoiner messageSj = new StringJoiner(";");

        for (String storeNo : storeNos) {
            LocalDate deliveryTime = LocalDate.now().plusDays(1);
            DataSyncCompareResult dataSyncCompareResult = old2NewSyncService.dataSyncResultHandle(deliveryTime, deliveryTime, Integer.parseInt(storeNo));
            if(dataSyncCompareResult.successFlag()){
                continue;
            }
            //没有同步数据
            List<OldDistOrderDTO> noSyncDataList = dataSyncCompareResult.getNoSyncDataList();
            //需要删除的数据
            List<OldDistOrderDTO> needDelDataList = dataSyncCompareResult.getNeedDelDataList();

            if(!CollectionUtils.isEmpty(noSyncDataList)){
                String noSyncOrderNos = noSyncDataList.stream().map(OldDistOrderDTO::getOuterOrderId).collect(Collectors.joining(","));
                messageSj.add("storeNo:"+storeNo+"监测截单时间同步数据异常-----没有同步的订单号如下------："+noSyncOrderNos+"\n|\r");
            }
            if(!CollectionUtils.isEmpty(needDelDataList)){
                String needDelOrderNos = needDelDataList.stream().map(OldDistOrderDTO::getOuterOrderId).collect(Collectors.joining(","));
                messageSj.add("storeNo:"+storeNo + "监测截单时间同步数据异常-----需要删除的订单号如下------："+needDelOrderNos);
            }
        }


        if(StringUtils.isEmpty(messageSj.toString())){
            return new ProcessResult(InstanceStatus.SUCCESS, "数据同步成功");
        }

        HashMap<String, String> msgMap = new HashMap<>();
        msgMap.put("text",messageSj.toString());
        msgMap.put("title","截单时间对比告警机器通知");
        //配置中发送群机器人的url
        DingTalkRobotUtil.sendMsgAndAtAll("markdown", "https://open.feishu.cn/open-apis/bot/v2/hook/fbf53adc-e6fe-4790-806d-1026c0d4e377", () -> msgMap);
        return new ProcessResult(InstanceStatus.SUCCESS, messageSj.toString());
    }

}
