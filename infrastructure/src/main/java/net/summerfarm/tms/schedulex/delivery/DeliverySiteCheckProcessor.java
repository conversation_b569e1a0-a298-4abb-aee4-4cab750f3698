package net.summerfarm.tms.schedulex.delivery;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.delivery.DeliveryBatchRepository;
import net.summerfarm.tms.delivery.DeliverySiteRepository;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.enums.DeliveryBatchTypeEnum;
import net.summerfarm.tms.enums.DistOrderFulfillmentDeliveryWayEnum;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 查询有委托单但是没有配送点位检查
 * date: 2023/1/19 11:05<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class DeliverySiteCheckProcessor extends XianMuJavaProcessorV2 {
    @Resource
    private DeliveryBatchRepository deliveryBatchRepository;
    @Resource
    private DeliverySiteRepository deliverySiteRepository;
    @Resource
    private DistOrderRepository distOrderRepository;
    @Resource
    private SiteRepository siteRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("start DeliverySiteCheckProcessor:{}", context.getInstanceParameters());
        LocalDate bizDate = LocalDate.now();
        if (StringUtils.isEmpty(context.getInstanceParameters())) {
            bizDate = LocalDate.now().plusDays(1);
        } else {
            bizDate = LocalDate.parse(context.getInstanceParameters(), DateTimeFormatter.ISO_LOCAL_DATE);
        }
        //查询后一天的城配的批次信息
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder()
                .deliveryTime(bizDate.atStartOfDay())
                .deliveryBatchTypeList(Collections.singletonList(DeliveryBatchTypeEnum.city.getCode()))
                .build());
        if (!CollectionUtils.isEmpty(deliveryBatchEntityList)){
            List<Long> deliveryBatchIdList = deliveryBatchEntityList.stream().map(DeliveryBatchEntity::getId).collect(Collectors.toList());
            //根据批次信息查询配送点位信息
            List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryList(DeliverySiteQuery.builder()
                    .batchIdList(deliveryBatchIdList)
                    .build());
            if(!CollectionUtils.isEmpty(deliverySiteEntities)){
                List<Long> siteIdList = deliverySiteEntities.stream()
                        .map(deliverySiteEntity -> deliverySiteEntity.getSiteEntity().getId()).collect(Collectors.toList());
                //查询委托单信息不在配送点位里面
                List<DistOrderEntity> distOrderEntityList = distOrderRepository.queryList(DistOrderQuery.builder()
                        .expectBeginTime(bizDate.atStartOfDay())
                        .endSiteIdsNotIn(siteIdList)
                        .sources(DistOrderSourceEnum.getCityCode())
                        .build()
                );
                // 排除干线转运数据
                distOrderEntityList= distOrderEntityList.stream()
                        .filter(e -> !Objects.equals(e.getFulfillmentDeliveryWay(), DistOrderFulfillmentDeliveryWayEnum.TRUNK_TRANS.getCode()))
                        .collect(Collectors.toList());

                if(!CollectionUtils.isEmpty(distOrderEntityList)){
                    StringJoiner stringJoiner = new StringJoiner(";");
                    for (DistOrderEntity distOrderEntity : distOrderEntityList) {
                        distOrderEntity.setBeginSite(siteRepository.query(distOrderEntity.getBeginSite().getId()));
                        distOrderEntity.setEndSite(siteRepository.query(distOrderEntity.getEndSite().getId()));

                        stringJoiner.add(
                                "订单号:" + distOrderEntity.getDistClientVO().getOutOrderId() + "," +
                                        "配送时间:" + distOrderEntity.getDistFlowVO().getExpectBeginTime() + "," +
                                        "开始点位id:" + distOrderEntity.getBeginSite().getId() + "," +
                                        "结束点位id:" + distOrderEntity.getEndSite().getId()
                        );
                    }


                    distOrderEntityList.stream().map(dist -> dist.getEndSite().getId());
                    return new ProcessResult(InstanceStatus.FAILED, "存在没有生成的点位信息"+stringJoiner);
                }
            }
        }

        return new ProcessResult(InstanceStatus.SUCCESS, "定时任务比对成功");
    }
}
