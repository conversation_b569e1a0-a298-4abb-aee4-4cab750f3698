package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.converter.SiteConverter;
import net.summerfarm.tms.converter.TmsDeliveryOrderConverter;
import net.summerfarm.tms.converter.TmsDistItemConverter;
import net.summerfarm.tms.converter.TmsDistOrderConverter;
import net.summerfarm.tms.dao.TmsDeliveryOrder;
import net.summerfarm.tms.dao.TmsDistItem;
import net.summerfarm.tms.dao.TmsDistOrder;
import net.summerfarm.tms.dao.TmsDistSite;
import net.summerfarm.tms.delivery.entity.DeliveryOrderEntity;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.flatObject.DeliveryNoteOrderFlatObject;
import net.summerfarm.tms.dist.flatObject.ManyFulfillmentWayDistOrderFlatObject;
import net.summerfarm.tms.enums.DistOrderCancelTypeEnum;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.enums.DistOrderStatusEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.mapper.TmsDeliveryOrderMapper;
import net.summerfarm.tms.mapper.TmsDistItemMapper;
import net.summerfarm.tms.mapper.TmsDistOrderMapper;
import net.summerfarm.tms.mapper.TmsDistSiteMapper;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.summerfarm.tms.utils.MybatisPlusUtil;
import net.summerfarm.tms.utils.PageInfoHelper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:
 * date: 2022/9/14 16:51
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class DistOrderRepositoryImpl implements DistOrderRepository {

    private final TmsDistOrderMapper tmsDistOrderMapper;

    private final TmsDistItemMapper tmsDistItemMapper;

    private final TmsDeliveryOrderMapper tmsDeliveryOrderMapper;

    private final TmsDistSiteMapper tmsDistSiteMapper;

    @Override
    public PageInfo<DistOrderEntity> queryPage(DistOrderQuery distOrderQuery) {
        PageHelper.startPage(distOrderQuery.getPageIndex(), distOrderQuery.getPageSize());
        List<TmsDistOrder> tmsDistOrders = tmsDistOrderMapper.selectByQuery(distOrderQuery);
        PageInfo pageInfo = PageInfoHelper.createPageInfo(tmsDistOrders);
        pageInfo.setList(tmsDistOrders.stream().map(TmsDistOrderConverter::tmsDistOrder2Entity).collect(Collectors.toList()));
        //查询委托单详情
        List<Long> distIdList = tmsDistOrders.stream().map(TmsDistOrder::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(distIdList)){
            return pageInfo;
        }
        List<TmsDistItem> tmsDistItems = tmsDistItemMapper.selectList(new LambdaQueryWrapper<TmsDistItem>().in(TmsDistItem::getDistOrderId, distIdList));
        if(CollectionUtils.isEmpty(tmsDistItems)){
            return pageInfo;
        }
        Map<Long, List<TmsDistItem>> distIdDistItemMap = tmsDistItems.stream().collect(Collectors.groupingBy(TmsDistItem::getDistOrderId));
        List<DistOrderEntity> distOrderEntities = tmsDistOrders.stream().map(tmsDistOrder -> {
            List<TmsDistItem> items = distIdDistItemMap.get(tmsDistOrder.getId());
            return TmsDistOrderConverter.tmsDistOrder2Entity(tmsDistOrder, items == null ? new ArrayList<>() : items);
        }).collect(Collectors.toList());

        pageInfo.setList(distOrderEntities);
        return pageInfo;
    }

    @Override
    public DistOrderEntity queryWithItemByOutOrderIdAndSource(String outOrderId, DistOrderSourceEnum source) {
        LambdaQueryWrapper<TmsDistOrder> query = new LambdaQueryWrapper<>();
        query.eq(TmsDistOrder::getOuterOrderId, outOrderId);
        query.eq(TmsDistOrder::getSource, source.getCode());
        query.orderByDesc(TmsDistOrder::getId);
        query.last("limit 1");
        TmsDistOrder tmsDistOrder = tmsDistOrderMapper.selectOne(query);
        List<TmsDistItem> items = new ArrayList<>();
        if (tmsDistOrder != null) {
            items = tmsDistItemMapper.selectList(new LambdaQueryWrapper<TmsDistItem>().eq(TmsDistItem::getDistOrderId, tmsDistOrder.getId()));
        }
        return TmsDistOrderConverter.tmsDistOrder2Entity(tmsDistOrder, items);
    }

    @Override
    public DistOrderEntity queryWithItemByUk(String outOrderId, DistOrderSourceEnum source, LocalDateTime expectBeginTime, String outerContactId) {
        TmsDistOrder tmsDistOrder = getTmsDistOrderByUk(outOrderId, source, expectBeginTime, outerContactId);
        if (tmsDistOrder == null){
            return null;
        }
        List<TmsDistItem> items = tmsDistItemMapper.selectList(new LambdaQueryWrapper<TmsDistItem>().eq(TmsDistItem::getDistOrderId, tmsDistOrder.getId()));
        return TmsDistOrderConverter.tmsDistOrder2Entity(tmsDistOrder, items);
    }

    @Override
    public DistOrderEntity queryWithDeliveryOrderByUk(String outOrderId, DistOrderSourceEnum source, LocalDateTime expectBeginTime, String outerContactId) {
        DistOrderEntity distOrderEntity = this.queryByUk(outOrderId, source, expectBeginTime, outerContactId);
        if (distOrderEntity == null){
            return null;
        }
        List<TmsDeliveryOrder> deliveryOrders = getTmsDeliveryOrders(distOrderEntity.getDistId());
        distOrderEntity.setDeliveryOrders(deliveryOrders.stream().map(TmsDeliveryOrderConverter::do2Entity).collect(Collectors.toList()));
        return distOrderEntity;
    }

    @Override
    public DistOrderEntity queryWithItemWithSiteByUk(String outOrderId, DistOrderSourceEnum source, LocalDateTime expectBeginTime, String outerContactId) {
        DistOrderEntity distOrderEntity = this.queryWithItemByUk(outOrderId, source, expectBeginTime, outerContactId);
        if (distOrderEntity == null){
            return null;
        }
        Long beginSiteId = distOrderEntity.getBeginSite().getId();
        Long midSiteId = distOrderEntity.getMidSite().getId();
        Long endSiteId = distOrderEntity.getEndSite().getId();
        distOrderEntity.setBeginSite(SiteConverter.tmsDistSite2Entity(tmsDistSiteMapper.selectById(beginSiteId)));
        if (midSiteId != null) {
            distOrderEntity.setMidSite(SiteConverter.tmsDistSite2Entity(tmsDistSiteMapper.selectById(midSiteId)));
        }
        distOrderEntity.setEndSite(SiteConverter.tmsDistSite2Entity(tmsDistSiteMapper.selectById(endSiteId)));
        return distOrderEntity;
    }

    @Override
    public DistOrderEntity queryWithItemWithDeliveryOrderByUk(String outOrderId, DistOrderSourceEnum source, LocalDateTime expectBeginTime, String outerContactId) {
        DistOrderEntity distOrderEntity = this.queryWithItemByUk(outOrderId, source, expectBeginTime, outerContactId);
        if (distOrderEntity == null){
            return null;
        }
        List<TmsDeliveryOrder> deliveryOrders = getTmsDeliveryOrders(distOrderEntity.getDistId());
        distOrderEntity.setDeliveryOrders(deliveryOrders.stream().map(TmsDeliveryOrderConverter::do2Entity).collect(Collectors.toList()));
        return distOrderEntity;
    }

    @Override
    public DistOrderEntity queryWithItemWithSiteWithDeliveryOrderByUk(String outOrderId, DistOrderSourceEnum source, LocalDateTime expectBeginTime, String outerContactId) {
        DistOrderEntity distOrderEntity = this.queryWithItemWithSiteByUk(outOrderId, source, expectBeginTime, outerContactId);
        if (distOrderEntity == null){
            return null;
        }
        List<TmsDeliveryOrder> deliveryOrders = getTmsDeliveryOrders(distOrderEntity.getDistId());
        distOrderEntity.setDeliveryOrders(deliveryOrders.stream().map(TmsDeliveryOrderConverter::do2Entity).collect(Collectors.toList()));
        return distOrderEntity;
    }

    private List<TmsDeliveryOrder> getTmsDeliveryOrders(Long distId) {
        return tmsDeliveryOrderMapper.selectList(new LambdaQueryWrapper<TmsDeliveryOrder>().eq(TmsDeliveryOrder::getDistOrderId, distId));
    }

    @Override
    public DistOrderEntity queryByUk(String outOrderId, DistOrderSourceEnum source, LocalDateTime expectBeginTime, String outerContactId) {
        TmsDistOrder tmsDistOrder = getTmsDistOrderByUk(outOrderId, source, expectBeginTime, outerContactId);
        if (tmsDistOrder == null){
            return null;
        }
        return TmsDistOrderConverter.tmsDistOrder2Entity(tmsDistOrder);
    }

    private TmsDistOrder getTmsDistOrderByUk(String outOrderId, DistOrderSourceEnum source, LocalDateTime expectBeginTime, String outerContactId) {
        LambdaQueryWrapper<TmsDistOrder> query = new LambdaQueryWrapper<>();
        query.eq(TmsDistOrder::getOuterOrderId, outOrderId);
        query.eq(TmsDistOrder::getSource, source.getCode());
        query.eq(expectBeginTime != null, TmsDistOrder::getExpectBeginTime, expectBeginTime);
        query.eq(outerContactId != null, TmsDistOrder::getOuterContactId, outerContactId);
        query.orderByDesc(TmsDistOrder::getId).last("limit 1");
        TmsDistOrder tmsDistOrder = tmsDistOrderMapper.selectOne(query);
        // todo 兼容POP来源查询，后续数据处理完成可以删掉
        if(tmsDistOrder == null && (source == DistOrderSourceEnum.XM_MALL || source == DistOrderSourceEnum.XM_AFTER_SALE)){
            switch (source){
                case XM_MALL:
                    query.clear();
                    query.eq(TmsDistOrder::getOuterOrderId, outOrderId);
                    query.eq(TmsDistOrder::getSource, DistOrderSourceEnum.POP_MALL.getCode());
                    query.eq(expectBeginTime != null, TmsDistOrder::getExpectBeginTime, expectBeginTime);
                    query.eq(outerContactId != null, TmsDistOrder::getOuterContactId, outerContactId);
                    query.orderByDesc(TmsDistOrder::getId).last("limit 1");
                    tmsDistOrder = tmsDistOrderMapper.selectOne(query);
                    break;
                case XM_AFTER_SALE:
                    query.clear();
                    query.eq(TmsDistOrder::getOuterOrderId, outOrderId);
                    query.eq(TmsDistOrder::getSource, DistOrderSourceEnum.POP_AFTER_SALE.getCode());
                    query.eq(expectBeginTime != null, TmsDistOrder::getExpectBeginTime, expectBeginTime);
                    query.eq(outerContactId != null, TmsDistOrder::getOuterContactId, outerContactId);
                    query.orderByDesc(TmsDistOrder::getId).last("limit 1");
                    tmsDistOrder = tmsDistOrderMapper.selectOne(query);
                    break;
                default:
                    break;
            }
        }

        return tmsDistOrder;
    }

    @Override
    public Long save(DistOrderEntity distOrderEntity) {
        TmsDistOrder tmsDistOrder = TmsDistOrderConverter.entity2TmsDistOrder(distOrderEntity);
        tmsDistOrderMapper.insert(tmsDistOrder);
        Long distId = tmsDistOrder.getId();
        //处理配送物品
        List<TmsDistItem> items = distOrderEntity.getDistItems().stream().map(itemVo -> {
            TmsDistItem tmsDistItem = TmsDistItemConverter.vo2TmsDistItem(itemVo);
            tmsDistItem.setDistOrderId(distId);
            return tmsDistItem;
        }).collect(Collectors.toList());
        items.forEach(tmsDistItemMapper::insert);
        return distId;
    }
    

    @Override
    public Long saveOrUpdate(DistOrderEntity distOrderEntity) {
        String outOrderId = distOrderEntity.getDistClientVO().getOutOrderId();
        DistOrderSourceEnum source = distOrderEntity.getSource();
        LocalDateTime expectBeginTime = distOrderEntity.getDistFlowVO().getExpectBeginTime();
        String outContactId = distOrderEntity.getDistClientVO().getOutContactId();
        DistOrderEntity existedDistOrder = this.queryByUk(outOrderId, source, expectBeginTime, outContactId);
        if (existedDistOrder == null) {
            return this.save(distOrderEntity);

        }
        Long distId = existedDistOrder.getDistId();
        //处理配送物品
        tmsDistItemMapper.delete(new LambdaQueryWrapper<TmsDistItem>()
                .eq(TmsDistItem::getDistOrderId, distId));
        distOrderEntity.getDistItems().stream()
                .map(itemVo -> {
                    TmsDistItem tmsDistItem = TmsDistItemConverter.vo2TmsDistItem(itemVo);
                    tmsDistItem.setDistOrderId(distId);
                    return tmsDistItem;
                })
                .collect(Collectors.toList())
                .forEach(tmsDistItemMapper::insert);
        TmsDistOrder tmsDistOrder = TmsDistOrderConverter.entity2TmsDistOrder(distOrderEntity);
        tmsDistOrder.setId(distId);
        tmsDistOrderMapper.updateById(tmsDistOrder);
        return distId;
    }

    


    @Override
    public DistOrderEntity queryWithItem(Long distId) {
        TmsDistOrder tmsDistOrder = tmsDistOrderMapper.selectById(distId);
        if (tmsDistOrder == null) {
            return null;
        }
        List<TmsDistItem> items = tmsDistItemMapper.selectList(new LambdaQueryWrapper<TmsDistItem>().eq(TmsDistItem::getDistOrderId, distId));
        return TmsDistOrderConverter.tmsDistOrder2Entity(tmsDistOrder, items);
    }

    @Override
    public DistOrderEntity queryDetail(Long distId) {
        TmsDistOrder tmsDistOrder = tmsDistOrderMapper.selectById(distId);
        if (tmsDistOrder == null) {
            return null;
        }
        List<TmsDistItem> items = tmsDistItemMapper.selectList(new LambdaQueryWrapper<TmsDistItem>().eq(TmsDistItem::getDistOrderId, distId));
        List<TmsDeliveryOrder> deliveryOrders = tmsDeliveryOrderMapper.selectList(new LambdaQueryWrapper<TmsDeliveryOrder>().eq(TmsDeliveryOrder::getDistOrderId, tmsDistOrder.getId()));
        DistOrderEntity distOrderEntity = TmsDistOrderConverter.tmsDistOrder2Entity(tmsDistOrder, items, deliveryOrders);
        distOrderEntity.setBeginSite(SiteConverter.tmsDistSite2Entity(tmsDistSiteMapper.selectById(tmsDistOrder.getBeginSiteId())));
        if (tmsDistOrder.getMidSiteId() != null) {
            distOrderEntity.setMidSite(SiteConverter.tmsDistSite2Entity(tmsDistSiteMapper.selectById(tmsDistOrder.getMidSiteId())));
        }
        distOrderEntity.setEndSite(SiteConverter.tmsDistSite2Entity(tmsDistSiteMapper.selectById(tmsDistOrder.getEndSiteId())));
        return distOrderEntity;
    }

    @Override
    public Long queryCount(Long distId) {
        return tmsDistOrderMapper.selectCount(new LambdaQueryWrapper<TmsDistOrder>().eq(TmsDistOrder::getId, distId));
    }

    @Override
    public DistOrderEntity query(Long distOrderId) {
        return TmsDistOrderConverter.tmsDistOrder2Entity(tmsDistOrderMapper.selectById(distOrderId));
    }

    @Override
    public DistOrderEntity queryWithDeliveryOrder(Long distId) {
        DistOrderEntity distOrderEntity = this.query(distId);
        if (distOrderEntity == null) {
            return null;
        }
        List<TmsDeliveryOrder> deliveryOrders = getTmsDeliveryOrders(distOrderEntity.getDistId());
        distOrderEntity.setDeliveryOrders(deliveryOrders.stream().map(TmsDeliveryOrderConverter::do2Entity).collect(Collectors.toList()));
        return distOrderEntity;
    }

    @Override
    public void modifyStatus(DistOrderEntity distOrderEntity) {
        TmsDistOrder tmsDistOrder = TmsDistOrderConverter.entity2TmsDistOrder(distOrderEntity);
        tmsDistOrderMapper.updateById(tmsDistOrder);
    }

    @Override
    public void update(DistOrderEntity distOrderEntity) {
        TmsDistOrder tmsDistOrder = TmsDistOrderConverter.entity2TmsDistOrder(distOrderEntity);
        tmsDistOrderMapper.updateById(tmsDistOrder);
   
    }

    @Override
    public List<DistOrderEntity> queryByStatus(List<DistOrderStatusEnum> statusList) {
        List<Integer> stateList = statusList.stream().map(DistOrderStatusEnum::getCode).collect(Collectors.toList());

        List<TmsDistOrder> tmsDistOrders = tmsDistOrderMapper.selectList(new LambdaQueryWrapper<TmsDistOrder>()
                .in(TmsDistOrder::getState, stateList));
        return tmsDistOrders.stream().map(TmsDistOrderConverter::tmsDistOrder2Entity).collect(Collectors.toList());
    }

    @Override
    public List<DistOrderEntity> queryActDistOrderBySource(List<Integer> distOrderSourceCodes) {
        List<Integer> stateList = Arrays.asList(DistOrderStatusEnum.TO_BE_WIRED.getCode(), DistOrderStatusEnum.IN_DELIVERY.getCode());
        List<TmsDistOrder> tmsDistOrders = tmsDistOrderMapper.selectList(new LambdaQueryWrapper<TmsDistOrder>()
                .in(TmsDistOrder::getState, stateList)
                .in(TmsDistOrder::getSource, distOrderSourceCodes)
        );
        return tmsDistOrders.stream().map(TmsDistOrderConverter::tmsDistOrder2Entity).collect(Collectors.toList());
    }

    @Override
    public void remove(Long distId) {
        tmsDistOrderMapper.deleteById(distId);
        tmsDistItemMapper.delete(new LambdaQueryWrapper<TmsDistItem>().eq(TmsDistItem::getDistOrderId, distId));
    }

    @Override
    public List<DistOrderEntity> queryListWithItem(DistOrderQuery distOrderQuery) {
        List<TmsDistOrder> tmsDistOrders = tmsDistOrderMapper.selectList(
                new LambdaQueryWrapper<TmsDistOrder>()
                        .eq(distOrderQuery.getDistId() != null, TmsDistOrder::getId, distOrderQuery.getDistId())
                        .eq(distOrderQuery.getType() != null, TmsDistOrder::getType, distOrderQuery.getType())
                        .eq(distOrderQuery.getBeginSiteId() != null, TmsDistOrder::getBeginSiteId, distOrderQuery.getBeginSiteId())
                        .eq(distOrderQuery.getEndSiteId() != null, TmsDistOrder::getEndSiteId, distOrderQuery.getEndSiteId())
                        .eq(distOrderQuery.getExpectBeginTime() != null, TmsDistOrder::getExpectBeginTime, distOrderQuery.getExpectBeginTime())
                        .eq(distOrderQuery.getCancelType() != null, TmsDistOrder::getCancelType, distOrderQuery.getCancelType())
                        .ge(distOrderQuery.getDeliveryTime() != null, TmsDistOrder::getExpectBeginTime, distOrderQuery.getDeliveryTime() != null ? distOrderQuery.getDeliveryTime().atStartOfDay() : null)
                        .lt(distOrderQuery.getDeliveryTime() != null, TmsDistOrder::getExpectBeginTime, distOrderQuery.getDeliveryTime() != null ? LocalDateTime.of(distOrderQuery.getDeliveryTime(), Constants.localTimeMaxTime) : null)
                        .in(!CollectionUtils.isEmpty(distOrderQuery.getStateList()), TmsDistOrder::getState, distOrderQuery.getStateList())
                        .in(!CollectionUtils.isEmpty(distOrderQuery.getSources()), TmsDistOrder::getSource, distOrderQuery.getSources())
                        .in(!CollectionUtils.isEmpty(distOrderQuery.getStatus()), TmsDistOrder::getState, distOrderQuery.getStatus())
                        .in(!CollectionUtils.isEmpty(distOrderQuery.getEndSiteIds()), TmsDistOrder::getEndSiteId, distOrderQuery.getEndSiteIds())
                        .in(!CollectionUtils.isEmpty(distOrderQuery.getDistIdList()), TmsDistOrder::getId, distOrderQuery.getDistIdList())
        );

        //查询委托单详情
        List<Long> distIdList = tmsDistOrders.stream().map(TmsDistOrder::getId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(distIdList)){
            List<TmsDistItem> tmsDistItems = tmsDistItemMapper.selectList(new LambdaQueryWrapper<TmsDistItem>().in(TmsDistItem::getDistOrderId, distIdList));
            if(!CollectionUtils.isEmpty(tmsDistItems)){
                Map<Long, List<TmsDistItem>> distIdDistItemMap = tmsDistItems.stream().collect(Collectors.groupingBy(TmsDistItem::getDistOrderId));
                List<DistOrderEntity> distOrderEntities = tmsDistOrders.stream().map(tmsDistOrder -> {
                    List<TmsDistItem> items = distIdDistItemMap.get(tmsDistOrder.getId());
                    return TmsDistOrderConverter.tmsDistOrder2Entity(tmsDistOrder, items == null ? new ArrayList<>() : items);
                }).collect(Collectors.toList());

                return distOrderEntities;
            }else{
                return tmsDistOrders.stream().map(TmsDistOrderConverter::tmsDistOrder2Entity).collect(Collectors.toList());
            }
        }else{
            return tmsDistOrders.stream().map(TmsDistOrderConverter::tmsDistOrder2Entity).collect(Collectors.toList());
        }
    }

    @Override
    public List<DistOrderEntity> queryListWithDeliveryOrder(DistOrderQuery distOrderQuery) {
        List<DistOrderEntity> distOrderEntities = this.queryList(distOrderQuery);
        if (CollectionUtils.isEmpty(distOrderEntities)){
            return Collections.emptyList();
        }
        List<Long> distIdList = distOrderEntities.stream().map(DistOrderEntity::getDistId).collect(Collectors.toList());
        List<TmsDeliveryOrder> tmsDeliveryOrders = tmsDeliveryOrderMapper.selectList(new LambdaQueryWrapper<TmsDeliveryOrder>().in(TmsDeliveryOrder::getDistOrderId, distIdList));
        if (CollectionUtils.isEmpty(tmsDeliveryOrders)){
            return distOrderEntities;
        }
        Map<Long, List<TmsDeliveryOrder>> distIdDeliveryOrderMap = tmsDeliveryOrders.stream().collect(Collectors.groupingBy(TmsDeliveryOrder::getDistOrderId));
        for (DistOrderEntity distOrderEntity : distOrderEntities) {
            List<TmsDeliveryOrder> tmsDeliveryOrdersWithDistId = distIdDeliveryOrderMap.get(distOrderEntity.getDistId());
            List<DeliveryOrderEntity> deliveryOrderEntities = tmsDeliveryOrdersWithDistId.stream().map(TmsDeliveryOrderConverter::do2Entity).collect(Collectors.toList());
            distOrderEntity.setDeliveryOrders(deliveryOrderEntities);
        }
        return distOrderEntities;
    }

    @Override
    public List<DistOrderEntity> queryList(DistOrderQuery distOrderQuery) {
        List<TmsDistOrder> tmsDistOrders = tmsDistOrderMapper.selectList(
                new LambdaQueryWrapper<TmsDistOrder>()
                        .ge(distOrderQuery.getDeliveryTime() != null, TmsDistOrder::getExpectBeginTime, distOrderQuery.getDeliveryTime() != null ? distOrderQuery.getDeliveryTime().atStartOfDay() : null)
                        .lt(distOrderQuery.getDeliveryTime() != null, TmsDistOrder::getExpectBeginTime, distOrderQuery.getDeliveryTime() != null ? LocalDateTime.of(distOrderQuery.getDeliveryTime(), Constants.localTimeMaxTime) : null)
                        .in(!CollectionUtils.isEmpty(distOrderQuery.getOuterOrderIds()), TmsDistOrder::getOuterOrderId, distOrderQuery.getOuterOrderIds())
                        .eq(distOrderQuery.getSource() != null, TmsDistOrder::getSource, distOrderQuery.getSource())
                        .in(!CollectionUtils.isEmpty(distOrderQuery.getSources()), TmsDistOrder::getSource, distOrderQuery.getSources())
                        .in(!CollectionUtils.isEmpty(distOrderQuery.getStatus()), TmsDistOrder::getState, distOrderQuery.getStatus())
                        .eq(!StringUtils.isEmpty(distOrderQuery.getOuterContactId()), TmsDistOrder::getOuterContactId, distOrderQuery.getOuterContactId())
                        .eq(distOrderQuery.getBeginSiteId() != null, TmsDistOrder::getBeginSiteId, distOrderQuery.getBeginSiteId())
                        .eq(distOrderQuery.getEndSiteId() != null, TmsDistOrder::getEndSiteId, distOrderQuery.getEndSiteId())
                        .eq(distOrderQuery.getType() != null, TmsDistOrder::getType, distOrderQuery.getType())
                        .eq(distOrderQuery.getExpectBeginTime() != null, TmsDistOrder::getExpectBeginTime, distOrderQuery.getExpectBeginTime())
                        .notIn(!CollectionUtils.isEmpty(distOrderQuery.getEndSiteIdsNotIn()), TmsDistOrder::getEndSiteId, distOrderQuery.getEndSiteIdsNotIn())
                        .ge(distOrderQuery.getGeExpectBeginTime() != null,TmsDistOrder::getExpectBeginTime,distOrderQuery.getGeExpectBeginTime())
                        .le(distOrderQuery.getLeExpectBeginTime() != null,TmsDistOrder::getExpectBeginTime,distOrderQuery.getLeExpectBeginTime())
                        .in(!CollectionUtils.isEmpty(distOrderQuery.getDistIdList()), TmsDistOrder::getId, distOrderQuery.getDistIdList())
                        .in(!CollectionUtils.isEmpty(distOrderQuery.getFulfillmentDeliveryWays()),TmsDistOrder::getFulfillmentDeliveryWay,distOrderQuery.getFulfillmentDeliveryWays())
        );
        return tmsDistOrders.stream().map(TmsDistOrderConverter::tmsDistOrder2Entity).collect(Collectors.toList());
    }

    @Override
    public DistOrderEntity query(DistOrderQuery distOrderQuery) {
        TmsDistOrder tmsDistOrder = tmsDistOrderMapper.selectOne(
                new LambdaQueryWrapper<TmsDistOrder>()
                        .ge(distOrderQuery.getDeliveryTime() != null, TmsDistOrder::getExpectBeginTime, distOrderQuery.getDeliveryTime() != null ? distOrderQuery.getDeliveryTime().atStartOfDay() : null)
                        .lt(distOrderQuery.getDeliveryTime() != null, TmsDistOrder::getExpectBeginTime, distOrderQuery.getDeliveryTime() != null ? LocalDateTime.of(distOrderQuery.getDeliveryTime(), Constants.localTimeMaxTime) : null)
                        .in(!CollectionUtils.isEmpty(distOrderQuery.getOuterOrderIds()), TmsDistOrder::getOuterOrderId, distOrderQuery.getOuterOrderIds())
                        .eq(distOrderQuery.getSource() != null, TmsDistOrder::getSource, distOrderQuery.getSource())
                        .in(distOrderQuery.getSources() != null, TmsDistOrder::getSource, distOrderQuery.getSources())
                        .in(!CollectionUtils.isEmpty(distOrderQuery.getStatus()), TmsDistOrder::getState, distOrderQuery.getStatus())
                        .eq(!StringUtils.isEmpty(distOrderQuery.getOuterContactId()), TmsDistOrder::getOuterContactId, distOrderQuery.getOuterContactId())
                        .eq(distOrderQuery.getBeginSiteId() != null, TmsDistOrder::getBeginSiteId, distOrderQuery.getBeginSiteId())
                        .eq(distOrderQuery.getEndSiteId() != null, TmsDistOrder::getEndSiteId, distOrderQuery.getEndSiteId())
                        .eq(distOrderQuery.getType() != null, TmsDistOrder::getType, distOrderQuery.getType())
                        .eq(distOrderQuery.getOuterOrderId() != null, TmsDistOrder::getOuterOrderId, distOrderQuery.getOuterOrderId())
        );
        return TmsDistOrderConverter.tmsDistOrder2Entity(tmsDistOrder);
    }

    @Override
    public DistOrderEntity queryWithItemWithBeginSite(DistOrderQuery distOrderQuery) {
        TmsDistOrder tmsDistOrder = tmsDistOrderMapper.selectOne(
                new LambdaQueryWrapper<TmsDistOrder>()
                        .eq(distOrderQuery.getBeginSiteId() != null,TmsDistOrder::getBeginSiteId,distOrderQuery.getBeginSiteId())
                        .eq(distOrderQuery.getDistId() != null, TmsDistOrder::getId, distOrderQuery.getDistId())
                        .ge(distOrderQuery.getDeliveryTime() != null, TmsDistOrder::getExpectBeginTime, distOrderQuery.getDeliveryTime() != null ? distOrderQuery.getDeliveryTime().atStartOfDay() : null)
                        .lt(distOrderQuery.getDeliveryTime() != null, TmsDistOrder::getExpectBeginTime, distOrderQuery.getDeliveryTime() != null ? LocalDateTime.of(distOrderQuery.getDeliveryTime(), Constants.localTimeMaxTime) : null)
                        .eq(!StringUtils.isEmpty(distOrderQuery.getOuterContactId()), TmsDistOrder::getOuterContactId, distOrderQuery.getOuterContactId())
                        .eq(distOrderQuery.getOuterOrderId() != null, TmsDistOrder::getOuterOrderId, distOrderQuery.getOuterOrderId())
        );
        if(tmsDistOrder == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"委托单"+distOrderQuery.getOuterOrderId());
        }
        //查询委托单详情
        List<TmsDistItem> items = tmsDistItemMapper.selectList(new LambdaQueryWrapper<TmsDistItem>().eq(TmsDistItem::getDistOrderId, tmsDistOrder.getId()));

        DistOrderEntity distOrderEntity = TmsDistOrderConverter.tmsDistOrder2Entity(tmsDistOrder, items);
        distOrderEntity.setBeginSite(SiteConverter.tmsDistSite2Entity(tmsDistSiteMapper.selectById(tmsDistOrder.getBeginSiteId())));
        return distOrderEntity;
    }

    @Override
    public List<DistOrderEntity> queryListWithItemWithBeginSite(DistOrderQuery distOrderQuery) {
        if(CollectionUtils.isEmpty(distOrderQuery.getDistIdList())){
            return new ArrayList<>();
        }
        List<TmsDistOrder> tmsDistOrders = tmsDistOrderMapper.selectList(
                new LambdaQueryWrapper<TmsDistOrder>()
                        .in(!CollectionUtils.isEmpty(distOrderQuery.getDistIdList()), TmsDistOrder::getId, distOrderQuery.getDistIdList())
        );

        List<Long> distIdList = tmsDistOrders.stream().map(TmsDistOrder::getId).collect(Collectors.toList());
        //查询委托单详情
        List<TmsDistItem> items = tmsDistItemMapper.selectList(new LambdaQueryWrapper<TmsDistItem>()
                .in(TmsDistItem::getDistOrderId, distIdList)
        );
        //查询点位详情
        List<Long> siteIdLIst = tmsDistOrders.stream().map(TmsDistOrder::getBeginSiteId).collect(Collectors.toList());
        List<TmsDistSite> tmsDistSites = tmsDistSiteMapper.selectList(new LambdaQueryWrapper<TmsDistSite>().in(TmsDistSite::getId, siteIdLIst));

        Map<Long, List<TmsDistItem>> itemDistMap = items.stream().collect(Collectors.groupingBy(TmsDistItem::getDistOrderId));
        Map<Long, TmsDistSite> distSiteMap = tmsDistSites.stream().collect(Collectors.toMap(TmsDistSite::getId, Function.identity()));


        ArrayList<DistOrderEntity> distOrderEntities = new ArrayList<>();

        for (TmsDistOrder tmsDistOrder : tmsDistOrders) {
            List<TmsDistItem> tmsDistItems = itemDistMap.get(tmsDistOrder.getId()) == null ? new ArrayList<>() : itemDistMap.get(tmsDistOrder.getId());
            TmsDistSite beginSite = distSiteMap.get(tmsDistOrder.getBeginSiteId()) == null ? new TmsDistSite() : distSiteMap.get(tmsDistOrder.getBeginSiteId());

            DistOrderEntity distOrderEntity = TmsDistOrderConverter.tmsDistOrder2Entity(tmsDistOrder, tmsDistItems);
            distOrderEntity.setBeginSite(SiteConverter.tmsDistSite2Entity(beginSite));

            distOrderEntities.add(distOrderEntity);
        }

        return distOrderEntities;
    }

    @Override
    public DistOrderEntity queryWithSiteWithItem(Long distOrderId) {
        TmsDistOrder tmsDistOrder = tmsDistOrderMapper.selectById(distOrderId);
        TmsDistSite beginSiteEntity = tmsDistSiteMapper.selectById(tmsDistOrder.getBeginSiteId());
        TmsDistSite endSiteEntity = tmsDistSiteMapper.selectById(tmsDistOrder.getEndSiteId());

        List<TmsDistItem> tmsDistItems = tmsDistItemMapper.selectList(new LambdaQueryWrapper<TmsDistItem>().eq(TmsDistItem::getDistOrderId, tmsDistOrder.getId()));
        DistOrderEntity distOrderEntity = TmsDistOrderConverter.tmsDistOrder2Entity(tmsDistOrder, tmsDistItems);
        distOrderEntity.setBeginSite(SiteConverter.tmsDistSite2Entity(beginSiteEntity));
        distOrderEntity.setEndSite(SiteConverter.tmsDistSite2Entity(endSiteEntity));

        return distOrderEntity;
    }

    @Override
    public void updateStatus(ArrayList<DistOrderEntity> updateDistOrderEntityList, DistOrderStatusEnum statusEnum) {
        if(CollectionUtils.isEmpty(updateDistOrderEntityList)){
            return;
        }
        List<Long> distIdList = updateDistOrderEntityList.stream().map(DistOrderEntity::getDistId).collect(Collectors.toList());

        UpdateWrapper<TmsDistOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id",distIdList);
        updateWrapper.set("state",statusEnum.getCode());

        tmsDistOrderMapper.update(null,updateWrapper);
    }

    @Override
    public Map<Long, String> querySiteShopName(DistOrderQuery distOrderQuery) {
        return tmsDistOrderMapper.selectList(new LambdaQueryWrapper<TmsDistOrder>()
                .eq(distOrderQuery.getBeginSiteId() != null, TmsDistOrder::getBeginSiteId, distOrderQuery.getBeginSiteId())
                .in(!CollectionUtils.isEmpty(distOrderQuery.getEndSiteIds()), TmsDistOrder::getEndSiteId, distOrderQuery.getEndSiteIds())
                .groupBy(TmsDistOrder::getEndSiteId)
                .select(TmsDistOrder::getEndSiteId, TmsDistOrder::getOuterClientName)
        ).stream().collect(Collectors.toMap(TmsDistOrder::getEndSiteId, TmsDistOrder::getOuterClientName));
    }

    @Override
    public void removeDistItem(Long distItemId) {
        tmsDistItemMapper.deleteById(distItemId);
    }

    @Override
    public List<DistOrderEntity> queryValidListWithItemByIds(List<Long> distOrderIdList) {
        if(CollectionUtils.isEmpty(distOrderIdList)){
            return Collections.emptyList();
        }
        List<TmsDistOrder> tmsDistOrders = tmsDistOrderMapper.selectList(
                new LambdaQueryWrapper<TmsDistOrder>().in(TmsDistOrder::getId, distOrderIdList)
        );

        //过滤拦截的订单
        tmsDistOrders = tmsDistOrders.stream()
                .filter(distOrder -> distOrder.getCancelType() != DistOrderCancelTypeEnum.intercept.getCode())
                .collect(Collectors.toList());

        //查询委托单详情
        List<Long> distIdList = tmsDistOrders.stream().map(TmsDistOrder::getId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(distIdList)){
            List<TmsDistItem> tmsDistItems = tmsDistItemMapper.selectList(new LambdaQueryWrapper<TmsDistItem>().in(TmsDistItem::getDistOrderId, distIdList));
            if(!CollectionUtils.isEmpty(tmsDistItems)){
                Map<Long, List<TmsDistItem>> distIdDistItemMap = tmsDistItems.stream().collect(Collectors.groupingBy(TmsDistItem::getDistOrderId));
                List<DistOrderEntity> distOrderEntities = tmsDistOrders.stream().map(tmsDistOrder -> {
                    List<TmsDistItem> items = distIdDistItemMap.get(tmsDistOrder.getId());
                    return TmsDistOrderConverter.tmsDistOrder2Entity(tmsDistOrder, items == null ? new ArrayList<>() : items);
                }).collect(Collectors.toList());

                return distOrderEntities;
            }else{
                return tmsDistOrders.stream().map(TmsDistOrderConverter::tmsDistOrder2Entity).collect(Collectors.toList());
            }
        }else{
            return tmsDistOrders.stream().map(TmsDistOrderConverter::tmsDistOrder2Entity).collect(Collectors.toList());
        }
    }

    @Override
    public List<DistOrderEntity> queryDistOrdersByBatchId(Long batchId) {
        //先查询配送单
        List<TmsDeliveryOrder> tmsDeliveryOrders = tmsDeliveryOrderMapper.selectList(new LambdaQueryWrapper<TmsDeliveryOrder>().eq(TmsDeliveryOrder::getBatchId, batchId));
        //在查询对应的委托单
        List<Long> distIdList = tmsDeliveryOrders.stream().map(TmsDeliveryOrder::getDistOrderId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(distIdList)) {
            return new ArrayList<>();
        }
        List<TmsDistOrder> tmsDistOrders = tmsDistOrderMapper.selectList(new LambdaQueryWrapper<TmsDistOrder>().in(TmsDistOrder::getId, distIdList));
        return tmsDistOrders.stream().map(TmsDistOrderConverter::tmsDistOrder2Entity).collect(Collectors.toList());
    }

    @Override
    public List<DistOrderEntity> queryDistOrdersWithItemByBatchId(Long batchId) {
        List<DistOrderEntity> distOrderEntities = this.queryDistOrdersByBatchId(batchId);
        List<Long> distIdList = distOrderEntities.stream().map(DistOrderEntity::getDistId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(distIdList)){
            return new ArrayList<>();
        }
        List<TmsDistItem> tmsDistItems = tmsDistItemMapper.selectList(new LambdaQueryWrapper<TmsDistItem>().in(TmsDistItem::getDistOrderId, distIdList));
        Map<Long, List<TmsDistItem>> distItemsMap = tmsDistItems.stream().collect(Collectors.groupingBy(TmsDistItem::getDistOrderId));
        distOrderEntities.forEach(e -> {
            List<TmsDistItem> items = distItemsMap.get(e.getDistId());
            if (items == null) {
                items = new ArrayList<>();
            }
            e.setDistItems(items.stream().map(TmsDistItemConverter::tmsDistItem2Vo).collect(Collectors.toList()));
            e.orderItemsStats();
        });
        return distOrderEntities;
    }

    @Override
    public DistOrderEntity queryLastWithDeliveryOrderForceMaster(DistOrderQuery distOrderQuery) {
        if(CollectionUtils.isEmpty(distOrderQuery.getSources()) || distOrderQuery.getExpectBeginTime() == null
                || StringUtils.isEmpty(distOrderQuery.getOuterContactId())){
            throw new TmsRuntimeException("查询条件不满足");
        }
        List<TmsDistOrder> tmsDistOrders = tmsDistOrderMapper.queryListForceMaster(distOrderQuery.getExpectBeginTime(),
                distOrderQuery.getOuterContactId(),
                distOrderQuery.getSources());

        if(CollectionUtils.isEmpty(tmsDistOrders)){
            return null;
        }
        List<TmsDeliveryOrder> tmsDeliveryOrders = tmsDeliveryOrderMapper.queryListByDistIdForceMaster(tmsDistOrders.get(0).getId());

        return TmsDistOrderConverter.tmsDistOrder2Entity(tmsDistOrders.get(0),new ArrayList<>(),tmsDeliveryOrders);
    }

    @Override
    public DistOrderEntity queryById(Long distId) {
        if(distId == null){
            throw new TmsRuntimeException("查询条件distId不能为空");
        }
        TmsDistOrder tmsDistOrder = tmsDistOrderMapper.selectById(distId);

        return TmsDistOrderConverter.tmsDistOrder2Entity(tmsDistOrder);
    }

    @Override
    public void batchUpdate(List<DistOrderEntity> distOrderEntities) {
        if(CollectionUtils.isEmpty(distOrderEntities)){
            return;
        }
        List<TmsDistOrder> tmsDistOrders = distOrderEntities.stream().map(TmsDistOrderConverter::entity2TmsDistOrder).collect(Collectors.toList());
        MybatisPlusUtil.updateBatch(tmsDistOrders,TmsDistOrder.class);
    }

    @Override
    public List<DeliveryNoteOrderFlatObject> queryDeliveryNoteByDateAndStoreNo(LocalDate deliveryTime, Integer storeNo) {
        if(deliveryTime == null || storeNo == null){
            return Collections.emptyList();
        }

        return tmsDistOrderMapper.queryDeliveryNoteByDateAndStoreNo(deliveryTime,storeNo);
    }

    @Override
    public List<DeliveryNoteOrderFlatObject> queryOrderDeliveryNoteByOrderNo(String orderNo) {
        if(StringUtils.isEmpty(orderNo)){
            return Collections.emptyList();
        }
        return tmsDistOrderMapper.queryOrderDeliveryNoteByOrderNo(orderNo);
    }

    @Override
    public List<Long> queryTrunkTransportBeginSites(LocalDateTime expectBeginTime, List<Integer> sources, Integer state) {
        if (expectBeginTime == null || CollectionUtils.isEmpty(sources) || state == null) {
            return Collections.emptyList();
        }
        return tmsDistOrderMapper.queryTrunkTransportBeginSites(expectBeginTime, sources, state);
    }

    @Override
    public List<ManyFulfillmentWayDistOrderFlatObject> queryHaveNextDeliveryDayManyFulfillmentWay(LocalDate deliveryTime, List<Integer> sourceList){
        if (deliveryTime == null || CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        return tmsDistOrderMapper.queryHaveNextDeliveryDayManyFulfillmentWay(deliveryTime, sourceList);
    }
}
