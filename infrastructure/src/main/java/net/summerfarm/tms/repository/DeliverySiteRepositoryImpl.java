package net.summerfarm.tms.repository;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.converter.*;
import net.summerfarm.tms.dao.*;
import net.summerfarm.tms.dataobj.DeliverySiteStandardTempConditionDataObj;
import net.summerfarm.tms.dataobj.converter.DeliverySiteStandardTempConditionDataConverter;
import net.summerfarm.tms.delivery.DeliverySiteRepository;
import net.summerfarm.tms.delivery.entity.*;
import net.summerfarm.tms.delivery.helper.DeliverySiteStandardTempCondition;
import net.summerfarm.tms.enums.DeliverySiteItemTypeEnum;
import net.summerfarm.tms.enums.DeliverySiteStatusEnum;
import net.summerfarm.tms.enums.DeliverySiteTypeEnum;
import net.summerfarm.tms.enums.TmsTemperatureEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.jsonobject.DeliverySitePropertyJson;
import net.summerfarm.tms.mapper.*;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.summerfarm.tms.utils.MybatisPlusUtil;
import net.summerfarm.tms.utils.PageInfoHelper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/9/13 18:16<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class DeliverySiteRepositoryImpl implements DeliverySiteRepository {
    @Resource
    private TmsDeliverySiteMapper tmsDeliverySiteMapper;
    @Resource
    private TmsDeliverySiteItemMapper tmsDeliverySiteItemMapper;
    @Resource
    private TmsDeliveryPickMapper tmsDeliveryPickMapper;
    @Resource
    private TmsDistSiteMapper tmsDistSiteMapper;
    @Resource
    private TmsDeliveryOrderMapper tmsDeliveryOrderMapper;
    @Resource
    private TmsDeliverySiteItemRecycleMapper tmsDeliverySiteItemRecycleMapper;

    @Override
    public List<DeliverySiteEntity> queryList(DeliverySiteQuery deliverySiteQuery) {

        List<TmsDeliverySite> tmsDeliverySites = tmsDeliverySiteMapper.selectList(
                new LambdaQueryWrapper<TmsDeliverySite>()
                        .eq(deliverySiteQuery.getBatchId() != null, TmsDeliverySite::getDeliveryBatchId, deliverySiteQuery.getBatchId())
                        .eq(deliverySiteQuery.getStatus() != null, TmsDeliverySite::getStatus, deliverySiteQuery.getStatus())
                        .eq(deliverySiteQuery.getSiteId() != null, TmsDeliverySite::getSiteId, deliverySiteQuery.getSiteId())
                        .eq(deliverySiteQuery.getInterceptState() != null, TmsDeliverySite::getInterceptState, deliverySiteQuery.getInterceptState())
                        .eq(deliverySiteQuery.getPlanArriveTime() != null, TmsDeliverySite::getPlanArriveTime, deliverySiteQuery.getPlanArriveTime())
                        .in(deliverySiteQuery.getInterceptStates() != null, TmsDeliverySite::getInterceptState, deliverySiteQuery.getInterceptStates())
                        .in(!CollectionUtils.isEmpty(deliverySiteQuery.getDeliverySiteIds()), TmsDeliverySite::getId, deliverySiteQuery.getDeliverySiteIds())
                        .le(deliverySiteQuery.getSignInTime() != null, TmsDeliverySite::getSignInTime, deliverySiteQuery.getSignInTime())
                        .in(!CollectionUtils.isEmpty(deliverySiteQuery.getBatchIdList()), TmsDeliverySite::getDeliveryBatchId, deliverySiteQuery.getBatchIdList())
                        .eq(deliverySiteQuery.getSendWay() != null, TmsDeliverySite::getSendWay, deliverySiteQuery.getSendWay())
                        .ne(deliverySiteQuery.getNotStatus() != null, TmsDeliverySite::getStatus, deliverySiteQuery.getNotStatus())
        );

        return tmsDeliverySites.stream().map(TmsDeliverySiteConverter::tmsDeliverySiteToSiteEntity).collect(Collectors.toList());
    }

    @Override
    public List<DeliverySiteEntity> queryListWithSite(DeliverySiteQuery deliverySiteQuery) {
        List<DeliverySiteEntity> deliverySiteEntities = queryList(deliverySiteQuery);
        deliverySiteEntities.forEach(deliverySiteEntity -> {
            TmsDistSite tmsDistSite = tmsDistSiteMapper.selectById(deliverySiteEntity.getSiteId());
            deliverySiteEntity.setSiteEntity(SiteConverter.tmsDistSite2Entity(tmsDistSite));
        });
        return deliverySiteEntities.stream().sorted(Comparator.comparing(DeliverySiteEntity::getSequence)).collect(Collectors.toList());
    }

    @Override
    public DeliverySiteEntity query(Long deliverySiteId) {
        return TmsDeliverySiteConverter.tmsDeliverySiteToSiteEntity(tmsDeliverySiteMapper.selectById(deliverySiteId));
    }

    @Override
    public DeliverySiteEntity queryWithSite(Long deliverySiteId) {
        DeliverySiteEntity deliverySiteEntity = query(deliverySiteId);
        deliverySiteEntity.setSiteEntity(SiteConverter.tmsDistSite2Entity(tmsDistSiteMapper.selectById(deliverySiteEntity.getSiteId())));
        return deliverySiteEntity;
    }

    @Override
    public DeliverySiteEntity queryWithItems(Long deliverySiteId) {
        DeliverySiteEntity deliverySiteEntity = query(deliverySiteId);
        deliverySiteEntity.setDeliverySiteItemEntityList(getItems(deliverySiteId));
        return deliverySiteEntity;
    }

    @Override
    public DeliverySiteEntity queryWithItems(DeliverySiteQuery deliverySiteQuery) {
        DeliverySiteEntity deliverySiteEntity = query(deliverySiteQuery);
        if(deliverySiteEntity == null || deliverySiteEntity.getId() == null){
            return deliverySiteEntity;
        }
        deliverySiteEntity.setDeliverySiteItemEntityList(getItems(deliverySiteEntity.getId()));
        return deliverySiteEntity;
    }

    @Override
    public DeliverySiteEntity queryWithItemsWithRecycle(DeliverySiteQuery deliverySiteQuery) {
        DeliverySiteEntity deliverySiteEntity = this.queryWithItems(deliverySiteQuery);
        List<DeliverySiteItemEntity> deliverySiteItemEntities = deliverySiteEntity.getDeliverySiteItemEntityList();
        if (CollectionUtils.isEmpty(deliverySiteItemEntities)){
            return deliverySiteEntity;
        }
        //获取回收点位物品ID
        List<Long> siteItemIds = deliverySiteItemEntities.stream().filter(item -> Objects.equals(item.getType(), DeliverySiteItemTypeEnum.RECYCLE.getCode())).map(DeliverySiteItemEntity::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(siteItemIds)){
            return deliverySiteEntity;
        }
        List<TmsDeliverySiteItemRecycle> tmsDeliverySiteItemRecycles = tmsDeliverySiteItemRecycleMapper.selectList(new LambdaQueryWrapper<TmsDeliverySiteItemRecycle>()
                .in(TmsDeliverySiteItemRecycle::getDeliverySiteItemId, siteItemIds));
        if (CollectionUtils.isEmpty(tmsDeliverySiteItemRecycles)){
            return deliverySiteEntity;
        }
        Map<Long, TmsDeliverySiteItemRecycle> deliverySiteItemIdRecycleMap = tmsDeliverySiteItemRecycles.stream().collect(Collectors.toMap(TmsDeliverySiteItemRecycle::getDeliverySiteItemId, Function.identity(), (oldData, newData) -> newData));
        for (DeliverySiteItemEntity deliverySiteItemEntity : deliverySiteItemEntities) {
            TmsDeliverySiteItemRecycle tmsDeliverySiteItemRecycle = deliverySiteItemIdRecycleMap.get(deliverySiteItemEntity.getId());
            if(tmsDeliverySiteItemRecycle == null){
                continue;
            }
            deliverySiteItemEntity.setDeliverySiteItemRecycleEntity(TmsDeliverySiteItemRecycleConverter.do2Entity(tmsDeliverySiteItemRecycle));
        }
        return deliverySiteEntity;
    }

    private List<DeliverySiteItemEntity> getItems(Long deliverySiteId) {
        List<TmsDeliverySiteItem> tmsDeliverySiteItems = tmsDeliverySiteItemMapper.selectList(
                new LambdaQueryWrapper<TmsDeliverySiteItem>().eq(TmsDeliverySiteItem::getDeliverySiteId, deliverySiteId));
        return tmsDeliverySiteItems.stream()
                .map(TmsDeliverySiteItemConverter::do2Entity)
                .collect(Collectors.toList());
    }

    @Override
    public DeliverySiteEntity queryWithPicks(Long deliverySiteId) {
        DeliverySiteEntity deliverySiteEntity = query(deliverySiteId);
        List<TmsDeliveryPick> tmsDeliveryPicks = tmsDeliveryPickMapper.selectList(
                new LambdaQueryWrapper<TmsDeliveryPick>().eq(TmsDeliveryPick::getDeliverySiteId, deliverySiteId));
        deliverySiteEntity.setDeliveryPickEntityList(
                tmsDeliveryPicks.stream()
                        .map(TmsDeliveryPickConverter::do2Entity)
                        .collect(Collectors.toList()));
        return deliverySiteEntity;
    }

    @Override
    public long queryCount(DeliverySiteQuery deliverySiteQuery) {
        return tmsDeliverySiteMapper.selectCount(new LambdaQueryWrapper<TmsDeliverySite>()
                .eq(deliverySiteQuery.getBatchId() != null, TmsDeliverySite::getDeliveryBatchId, deliverySiteQuery.getBatchId())
                .eq(deliverySiteQuery.getStatus() != null, TmsDeliverySite::getStatus, deliverySiteQuery.getStatus())
                .ne(deliverySiteQuery.getNotStatus() != null, TmsDeliverySite::getStatus, deliverySiteQuery.getNotStatus())
                .ne(deliverySiteQuery.getNotInterceptState() != null, TmsDeliverySite::getInterceptState, deliverySiteQuery.getNotInterceptState())
                .eq(deliverySiteQuery.getSiteId() != null, TmsDeliverySite::getSiteId, deliverySiteQuery.getSiteId())
                .eq(deliverySiteQuery.getSendWay() != null, TmsDeliverySite::getSendWay, deliverySiteQuery.getSendWay())
        );
    }

    @Resource
    private TmsDistOrderMapper tmsDistOrderMapper;

    @Override
    public void update(DeliverySiteEntity deliverySiteEntity) {

        tmsDeliverySiteMapper.updateById(TmsDeliverySiteConverter.entity2Do(deliverySiteEntity));
        if (!CollectionUtils.isEmpty(deliverySiteEntity.getDeliveryPickEntityList())) {
            for (DeliveryPickEntity deliveryPickEntity : deliverySiteEntity.getDeliveryPickEntityList()) {
                tmsDeliveryPickMapper.updateById(TmsDeliveryPickConverter.entity2Do(deliveryPickEntity));
            }
        }
        if (!CollectionUtils.isEmpty(deliverySiteEntity.getDeliverySiteItemEntityList())) {
            for (DeliverySiteItemEntity deliverySiteItemEntity : deliverySiteEntity.getDeliverySiteItemEntityList()) {
                tmsDeliverySiteItemMapper.updateById(TmsDeliverySiteItemConverter.entity2Do(deliverySiteItemEntity));
            }

        }
    }

    @Override
    public void save(List<DeliverySiteEntity> deliverySiteList, Long deliveryBatchId) {
        LocalDateTime now = LocalDateTime.now();

        for (int i = 0; i < deliverySiteList.size(); i++) {
            DeliverySiteEntity deliverySiteEntity = deliverySiteList.get(i);
            //判断该批次是否已存在该点位
            long siteCnt = tmsDeliverySiteMapper.selectCount(new LambdaQueryWrapper<TmsDeliverySite>()
                    .eq(TmsDeliverySite::getPlanArriveTime, deliverySiteEntity.getPlanArriveTime())
                    .eq(TmsDeliverySite::getDeliveryBatchId, deliveryBatchId)
                    .eq(TmsDeliverySite::getSiteId, deliverySiteEntity.getSiteId()));
            if (siteCnt > 0) {
                continue;
            }
            TmsDeliverySite tmsDeliverySite = new TmsDeliverySite();
            tmsDeliverySite.setCreateTime(now);
            tmsDeliverySite.setDeliveryBatchId(deliveryBatchId);
            tmsDeliverySite.setSiteId(deliverySiteList.get(i).getSiteId());
            tmsDeliverySite.setSequence(deliverySiteEntity.getSequence());
            tmsDeliverySite.setStatus(DeliverySiteStatusEnum.NO.getCode());
            tmsDeliverySite.setDistance(deliverySiteEntity.getDistance());
            tmsDeliverySite.setOuterClientId(deliverySiteEntity.getOuterClientId());
            tmsDeliverySite.setOuterClientName(deliverySiteEntity.getOuterClientName());
            tmsDeliverySite.setOuterBrandId(deliverySiteEntity.getOuterBrandId());
            tmsDeliverySite.setOuterBrandName(deliverySiteEntity.getOuterBrandName());
            tmsDeliverySite.setPlanArriveTime(deliverySiteEntity.getPlanArriveTime());
            tmsDeliverySite.setPlanOutTime(deliverySiteEntity.getPlanOutTime());
            //起点
            if (i == 0) {
                tmsDeliverySite.setType(DeliverySiteTypeEnum.begin.getCode());
            } else if (i == deliverySiteList.size() - 1) {
                //终点
                tmsDeliverySite.setType(DeliverySiteTypeEnum.end.getCode());
            } else {
                //途经点
                tmsDeliverySite.setType(DeliverySiteTypeEnum.way.getCode());
            }
            tmsDeliverySiteMapper.insert(tmsDeliverySite);
        }
    }

    @Override
    public DeliverySiteEntity queryById(Long id) {
        if(id == null){
            return null;
        }
        return TmsDeliverySiteConverter.tmsDeliverySiteToSiteEntity(tmsDeliverySiteMapper.selectById(id));
    }

    @Override
    public void updateSortAdd(Long deliveryBatchId,Integer sourceSeq, Integer newSequence) {
        try {
            tmsDeliverySiteMapper.updateSortAdd(deliveryBatchId, sourceSeq, newSequence);
        } catch (Exception e) {
            String errorMessage = e.getMessage();
            if (errorMessage.contains("Deadlock found") || errorMessage.contains("Lock wait timeout exceeded")) {
                throw new TmsRuntimeException("有人正在操作此排线信息，请稍后再试");
            }
            throw e;
        }
    }

    @Override
    public void updateSortSub(Long deliveryBatchId, Integer sourceSeq, Integer newSequence) {
        try {
            tmsDeliverySiteMapper.updateSortSub(deliveryBatchId,sourceSeq, newSequence);
        } catch (Exception e) {
            String errorMessage = e.getMessage();
            if (errorMessage.contains("Deadlock found") || errorMessage.contains("Lock wait timeout exceeded")) {
                throw new TmsRuntimeException("有人正在操作此排线信息，请稍后再试");
            }
            throw e;
        }
    }

    @Override
    public void batchRemoveByBatchId(Long batchId, List<Long> deliverySideIds) {
        tmsDeliverySiteMapper.delete(
                new LambdaQueryWrapper<TmsDeliverySite>()
                        .eq(TmsDeliverySite::getDeliveryBatchId, batchId)
                        .in(TmsDeliverySite::getId, deliverySideIds)
        );
    }

    @Override
    public void batchSave(List<DeliverySiteEntity> deliverySiteList) {
        if (CollectionUtils.isEmpty(deliverySiteList)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        for (int i = 0; i < deliverySiteList.size(); i++) {
            DeliverySiteEntity deliverySiteEntity = deliverySiteList.get(i);
            TmsDeliverySite tmsDeliverySite = new TmsDeliverySite();

            tmsDeliverySite.setCreateTime(now);
            tmsDeliverySite.setDeliveryBatchId(deliverySiteEntity.getDeliveryBatchId());
            tmsDeliverySite.setSiteId(deliverySiteList.get(i).getId());
            tmsDeliverySite.setSequence(i + 1);
            tmsDeliverySite.setStatus(DeliverySiteStatusEnum.NO.getCode());
            tmsDeliverySite.setPlanArriveTime(deliverySiteEntity.getPlanArriveTime());
            tmsDeliverySite.setPlanOutTime(deliverySiteEntity.getPlanOutTime());
            //起点
            if (i == 0) {
                tmsDeliverySite.setType(DeliverySiteTypeEnum.begin.getCode());
            } else if (i == deliverySiteList.size() - 1) {
                //终点
                tmsDeliverySite.setType(DeliverySiteTypeEnum.end.getCode());
            } else {
                //途经点
                tmsDeliverySite.setType(DeliverySiteTypeEnum.way.getCode());
            }
            tmsDeliverySiteMapper.insert(tmsDeliverySite);
        }
    }

    @Override
    public DeliverySiteEntity query(DeliverySiteQuery deliverySiteQuery) {
        TmsDeliverySite tmsDeliverySite = tmsDeliverySiteMapper.selectOne(new LambdaQueryWrapper<TmsDeliverySite>()
                .eq(deliverySiteQuery.getBatchId() != null, TmsDeliverySite::getDeliveryBatchId, deliverySiteQuery.getBatchId())
                .eq(deliverySiteQuery.getSiteId() != null, TmsDeliverySite::getSiteId, deliverySiteQuery.getSiteId())
                .eq(deliverySiteQuery.getDeliverySiteId() != null, TmsDeliverySite::getId, deliverySiteQuery.getDeliverySiteId())
                .eq(deliverySiteQuery.getSequence() != null, TmsDeliverySite::getSequence, deliverySiteQuery.getSequence())
                .ge(deliverySiteQuery.getDeliveryTime() != null, TmsDeliverySite::getPlanArriveTime, deliverySiteQuery.getDeliveryTime() != null ? deliverySiteQuery.getDeliveryTime().atStartOfDay() : null)
                .lt(deliverySiteQuery.getDeliveryTime() != null, TmsDeliverySite::getPlanArriveTime, deliverySiteQuery.getDeliveryTime() != null ? LocalDateTime.of(deliverySiteQuery.getDeliveryTime(), Constants.localTimeMaxTime) : null)
                .last("limit 1")
        );

        return TmsDeliverySiteConverter.tmsDeliverySiteToSiteEntity(tmsDeliverySite);
    }

    @Override
    public int remove(Long deliveryBatchId, Long distId, Long beginSiteId, Long endSiteId, LocalDateTime expectBeginTime) {
        int deleteRow = 0;
        //查询该配送点位当天是否还有其他配送单
        Long otherDistOrderCount = tmsDeliveryOrderMapper.selectCount(new LambdaQueryWrapper<TmsDeliveryOrder>()
                .eq(TmsDeliveryOrder::getBeginSiteId, beginSiteId)
                .eq(TmsDeliveryOrder::getEndSiteId, endSiteId)
                .eq(TmsDeliveryOrder::getDeliveryTime, expectBeginTime)
                .eq(TmsDeliveryOrder::getBatchId, deliveryBatchId)
                .ne(TmsDeliveryOrder::getDistOrderId, distId));
        if (otherDistOrderCount > 0) {
            //该点位还有其他委托单 不能删除该点位
            return deleteRow;
        }
        //删除配送点位
        deleteRow = tmsDeliverySiteMapper.delete(new LambdaQueryWrapper<TmsDeliverySite>()
                .eq(TmsDeliverySite::getDeliveryBatchId, deliveryBatchId)
                .eq(TmsDeliverySite::getSiteId, endSiteId));
        //删除失败根据主键再删除一次
        if (deleteRow == 0){
            TmsDeliverySite tmsDeliverySite = getTmsDeliveryOrderByUk(deliveryBatchId, endSiteId);
            if (tmsDeliverySite == null){
                return deleteRow;
            }
            deleteRow = tmsDeliverySiteMapper.deleteById(tmsDeliverySite.getId());
        }

        return deleteRow;
    }

    @Override
    public void removeById(Long deliverySiteId) {
        tmsDeliverySiteMapper.deleteById(deliverySiteId);
    }

    private TmsDeliverySite getTmsDeliveryOrderByUk(Long deliveryBatchId, Long endSiteId) {
        LambdaQueryWrapper<TmsDeliverySite> query = new LambdaQueryWrapper<>();
        query.eq(TmsDeliverySite::getDeliveryBatchId, deliveryBatchId);
        query.eq(TmsDeliverySite::getSiteId, endSiteId);
        return tmsDeliverySiteMapper.selectOne(query);
    }

    @Override
    public List<DeliverySiteEntity> queryWithSite(DeliverySiteQuery deliverySiteQuery) {
        List<TmsDeliverySite> tmsDeliverySites = tmsDeliverySiteMapper.selectList(
                new LambdaQueryWrapper<TmsDeliverySite>()
                        .eq(deliverySiteQuery.getBatchId() != null, TmsDeliverySite::getDeliveryBatchId, deliverySiteQuery.getBatchId())
                        .eq(deliverySiteQuery.getStatus() != null, TmsDeliverySite::getStatus, deliverySiteQuery.getStatus())
                        .eq(deliverySiteQuery.getSiteId() != null, TmsDeliverySite::getSiteId, deliverySiteQuery.getSiteId())
                        .eq(deliverySiteQuery.getInterceptState() != null, TmsDeliverySite::getInterceptState, deliverySiteQuery.getInterceptState())
                        .eq(deliverySiteQuery.getPlanArriveTime() != null, TmsDeliverySite::getPlanArriveTime, deliverySiteQuery.getPlanArriveTime())
                        .in(deliverySiteQuery.getInterceptStates() != null, TmsDeliverySite::getInterceptState, deliverySiteQuery.getInterceptStates())
                        .le(deliverySiteQuery.getSignInTime() != null, TmsDeliverySite::getSignInTime, deliverySiteQuery.getSignInTime())
                        .in(!CollectionUtils.isEmpty(deliverySiteQuery.getBatchIdList()), TmsDeliverySite::getDeliveryBatchId, deliverySiteQuery.getBatchIdList())
        );

        List<DeliverySiteEntity> deliverySiteEntities = tmsDeliverySites.stream().map(TmsDeliverySiteConverter::tmsDeliverySiteToSiteEntity).collect(Collectors.toList());

        List<Long> siteIdList = tmsDeliverySites.stream().map(TmsDeliverySite::getSiteId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(siteIdList)){
            List<TmsDistSite> tmsDistSites = tmsDistSiteMapper.selectList(
                    new LambdaQueryWrapper<TmsDistSite>().in(TmsDistSite::getId, siteIdList)
            );
            List<SiteEntity> siteEntities = tmsDistSites.stream().map(SiteConverter::tmsDistSite2Entity).collect(Collectors.toList());
            Map<Long, SiteEntity> siteIdEntityMap = siteEntities.stream().collect(Collectors.toMap(SiteEntity::getId, Function.identity()));

            for (DeliverySiteEntity deliverySiteEntity : deliverySiteEntities) {
                deliverySiteEntity.setSiteEntity(siteIdEntityMap.get(deliverySiteEntity.getSiteEntity().getId()));
            }
        }

        return deliverySiteEntities;
    }

    @Override
    public DeliverySiteEntity queryWithDistOrders(Long siteId) {
        if(siteId == null){
            return null;
        }
        TmsDeliverySite tmsDeliverySite = tmsDeliverySiteMapper.selectById(siteId);
        if(tmsDeliverySite == null){
            return null;
        }
        DeliverySiteEntity deliverySiteEntity = TmsDeliverySiteConverter.tmsDeliverySiteToSiteEntity(tmsDeliverySite);

        List<TmsDeliveryOrder> tmsDeliveryOrders = tmsDeliveryOrderMapper.selectList(new LambdaQueryWrapper<TmsDeliveryOrder>()
                .eq(TmsDeliveryOrder::getDeliveryTime, tmsDeliverySite.getPlanArriveTime().toLocalDate())
                .eq(TmsDeliveryOrder::getBatchId, tmsDeliverySite.getDeliveryBatchId())
                .eq(TmsDeliveryOrder::getEndSiteId, tmsDeliverySite.getSiteId())
        );
        if(!CollectionUtils.isEmpty(tmsDeliveryOrders)){
            List<TmsDistOrder> tmsDistOrders = tmsDistOrderMapper.selectList(new LambdaQueryWrapper<TmsDistOrder>()
                    .in(TmsDistOrder::getId, tmsDeliveryOrders.stream().map(TmsDeliveryOrder::getDistOrderId).collect(Collectors.toList()))
            );
            deliverySiteEntity.setSiteDistOrders(tmsDistOrders.stream().map(TmsDistOrderConverter::tmsDistOrder2Entity).collect(Collectors.toList()));
        }
        return deliverySiteEntity;
    }


    @Override
    public List<DeliverySiteEntity> queryWaitSendMsgList(Set<Long> batchIds, boolean arriveMsg) {
        if(CollectionUtils.isEmpty(batchIds)){
            return null;
        }
        List<DeliverySiteEntity> deliverySiteEntities = tmsDeliverySiteMapper.queryWaitSendMsgList(batchIds, arriveMsg);
        return deliverySiteEntities;
    }

    @Override
    public void updateSendMsgFlag(Set<Long> siteIds, boolean arriveMsg) {
        TmsDeliverySite tmsDeliverySite = new TmsDeliverySite();
        if (arriveMsg) {
            tmsDeliverySite.setArriveMsgSend(Boolean.TRUE);
        } else {
            tmsDeliverySite.setLeaveMsgSend(Boolean.TRUE);
        }
        tmsDeliverySiteMapper.update(tmsDeliverySite, new LambdaUpdateWrapper<TmsDeliverySite>()
                .in(TmsDeliverySite::getId, siteIds));
    }

    @Override
    public List<DeliverySiteEntity> queryListWithBatchSite(DeliverySiteQuery deliverySiteQuery) {
        List<DeliverySiteEntity> deliverySiteEntities = this.queryList(deliverySiteQuery);
        if (CollectionUtils.isEmpty(deliverySiteEntities)){
            return new ArrayList<>();
        }
        List<Long> siteIds = deliverySiteEntities.stream().map(DeliverySiteEntity::getSiteId).collect(Collectors.toList());
        List<TmsDistSite> tmsDistSites = tmsDistSiteMapper.selectList(new LambdaQueryWrapper<TmsDistSite>().in(TmsDistSite::getId, siteIds));
        Map<Long, TmsDistSite> tmsDistSiteMap = tmsDistSites.stream().collect(Collectors.toMap(TmsDistSite::getId, Function.identity(), (oldData, newData) -> newData));

        for (DeliverySiteEntity deliverySiteEntity : deliverySiteEntities) {
            TmsDistSite tmsDistSite = tmsDistSiteMap.get(deliverySiteEntity.getSiteId());
            deliverySiteEntity.setSiteEntity(SiteConverter.tmsDistSite2Entity(tmsDistSite));
        }
        return deliverySiteEntities;
    }

    @Override
    public PageInfo<CitySitePerformanceVO> queryCitySitePerformancePage(DeliverySiteQuery deliverySiteQuery) {
        PageHelper.startPage(deliverySiteQuery.getPageIndex(), deliverySiteQuery.getPageSize());
        List<CitySitePerformanceVO> citySitePerformanceList = tmsDeliverySiteMapper.queryCitySitePerformance(deliverySiteQuery);
        citySitePerformanceList.forEach(sitePerformance ->{
            if(StringUtils.isEmpty(sitePerformance.getPropertyJson())){
               return;
            }
            DeliverySitePropertyJson deliverySitePropertyJson = JSON.parseObject(sitePerformance.getPropertyJson(), DeliverySitePropertyJson.class);
            //车牌照片
            sitePerformance.setVehiclePlatePics(deliverySitePropertyJson.getVehiclePlatePics());
            // 冷藏照片
            sitePerformance.setRefrigeratePics(deliverySitePropertyJson.getRefrigeratePics());
            // 冷冻照片
            sitePerformance.setFreezePics(deliverySitePropertyJson.getFreezePics());
        });
        return  PageInfoHelper.createPageInfo(citySitePerformanceList);
    }

    @Override
    public DeliverySiteEntity queryForceMasterByBatchIdAndSiteId(Long deliveryBatchId, Long siteId) {
        return TmsDeliverySiteConverter.tmsDeliverySiteToSiteEntity(tmsDeliverySiteMapper.queryForceMasterByBatchIdAndSiteId(deliveryBatchId, siteId));
    }

    @Override
    public List<DeliverySiteEntity> queryListWithItemsByBatchIds(List<Long> deliveryBatchIds) {
        List<TmsDeliverySite> tmsDeliverySites = tmsDeliverySiteMapper.queryForceMasterByBatchIds(deliveryBatchIds);
        log.info("排线批次ID集合为：{},生成点位存储条件查询数据为：{}",deliveryBatchIds,JSON.toJSONString(tmsDeliverySites));
        if(CollectionUtils.isEmpty(tmsDeliverySites)){
            return Collections.emptyList();
        }
        List<DeliverySiteEntity> deliverySiteEntities = tmsDeliverySites.stream().map(TmsDeliverySiteConverter::tmsDeliverySiteToSiteEntity).collect(Collectors.toList());
        List<Long> deliverySiteIdList = tmsDeliverySites.stream().map(TmsDeliverySite::getId).collect(Collectors.toList());
        List<TmsDeliverySiteItem> tmsDeliverySiteItems = tmsDeliverySiteItemMapper.queryListForceMasterByDeliverySiteId(deliverySiteIdList);
        Map<Long, List<TmsDeliverySiteItem>> deliverySiteIdItemsMap = tmsDeliverySiteItems.stream().collect(Collectors.groupingBy(TmsDeliverySiteItem::getDeliverySiteId));

        for (DeliverySiteEntity deliverySiteEntity : deliverySiteEntities) {
            List<TmsDeliverySiteItem> siteItems = deliverySiteIdItemsMap.get(deliverySiteEntity.getId());
            if(!CollectionUtils.isEmpty(siteItems)){
                deliverySiteEntity.setDeliverySiteItemEntityList(siteItems.stream().map(TmsDeliverySiteItemConverter::do2Entity).collect(Collectors.toList()));
            }
        }

        return deliverySiteEntities;
    }


    @Override
    public void batchUpdate(List<DeliverySiteEntity> deliverySiteEntities) {
        if(CollectionUtils.isEmpty(deliverySiteEntities)){
            return;
        }
        List<TmsDeliverySite> tmsDeliverySites = deliverySiteEntities.stream().map(TmsDeliverySiteConverter::entity2Do).collect(Collectors.toList());
        tmsDeliverySites.sort(Comparator.comparing(TmsDeliverySite::getId));
        MybatisPlusUtil.updateBatch(tmsDeliverySites,TmsDeliverySite.class);
    }

    @Override
    public Boolean sameDeliverySiteHandle(LocalDateTime deliveryTime) {
        List<SameDeliverySite> sameDeliverySites = tmsDeliverySiteMapper.querySameSitesByDeliveryTime(deliveryTime);
        if (CollectionUtils.isEmpty(sameDeliverySites)){
            return Boolean.FALSE;
        }
        for (SameDeliverySite sameDeliverySite : sameDeliverySites) {
            List<TmsDeliverySite> tmsDeliverySites = tmsDeliverySiteMapper.queryListByBeginAndEndAndDeliveryTime(sameDeliverySite.getBeginSiteId(),
                    sameDeliverySite.getEndSiteId(), sameDeliverySite.getDeliveryTime());
            if (CollectionUtils.isEmpty(tmsDeliverySites) || tmsDeliverySites.size() < 2){
                continue;
            }
            for (TmsDeliverySite tmsDeliverySite : tmsDeliverySites) {
                //查询该点位是否有挂单子
                List<TmsDeliveryOrder> tmsDeliveryOrders = tmsDeliveryOrderMapper.selectList(new LambdaQueryWrapper<TmsDeliveryOrder>()
                        .eq(TmsDeliveryOrder::getBatchId, tmsDeliverySite.getDeliveryBatchId())
                        .eq(TmsDeliveryOrder::getEndSiteId, tmsDeliverySite.getSiteId())
                        .eq(TmsDeliveryOrder::getDeliveryTime, tmsDeliverySite.getPlanArriveTime()));
                log.info("相同配送时间相同点位信息:{},配送单信息:{}", JSON.toJSONString(tmsDeliverySite), JSON.toJSONString(tmsDeliveryOrders));
                if (CollectionUtils.isEmpty(tmsDeliveryOrders)){
                    tmsDeliverySiteMapper.deleteById(tmsDeliverySite.getId());
                    log.info("补偿任务删除配送点位ID:{}", tmsDeliverySite.getId());
                }
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public void updateAndSaveRecycle(DeliverySiteEntity deliverySiteEntity) {
        List<DeliverySiteItemEntity> deliverySiteItemEntityList = deliverySiteEntity.getDeliverySiteItemEntityList();
        if (!CollectionUtils.isEmpty(deliverySiteItemEntityList)){
            //处理回收类型物品
            List<DeliverySiteItemRecycleEntity> deliverySiteItemRecycleEntities = deliverySiteItemEntityList.stream()
                    .filter(e -> Objects.equals(DeliverySiteItemTypeEnum.RECYCLE.getCode(), e.getType()))
                    .filter(e -> e.getDeliverySiteItemRecycleEntity() != null)
                    .map(DeliverySiteItemEntity::getDeliverySiteItemRecycleEntity)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deliverySiteItemRecycleEntities)){
                List<TmsDeliverySiteItemRecycle> tmsDeliverySiteItemRecycles = deliverySiteItemRecycleEntities.stream().map(TmsDeliverySiteItemRecycleConverter::entity2Do).collect(Collectors.toList());
                MybatisPlusUtil.createBatch(tmsDeliverySiteItemRecycles, TmsDeliverySiteItemRecycle.class);
            }
        }
        this.update(deliverySiteEntity);
    }

    @Override
    public DeliverySiteEntity queryRecentDeliveryFinishSite(Long siteId) {
        TmsDeliverySite tmsDeliverySite = tmsDeliverySiteMapper.selectOne(new LambdaQueryWrapper<TmsDeliverySite>()
                .eq(TmsDeliverySite::getSiteId, siteId)
                .eq(TmsDeliverySite::getStatus, DeliverySiteStatusEnum.FINISH_DELIVERY.getCode())
                .orderByDesc(TmsDeliverySite::getId).last("limit 1"));
        return TmsDeliverySiteConverter.tmsDeliverySiteToSiteEntity(tmsDeliverySite);
    }

    @Override
    public Boolean deliverySiteWithNoOrderHandle(LocalDateTime deliveryTime) {
        List<TmsDeliverySite> tmsDeliverySites = tmsDeliverySiteMapper.querySiteWithNoOrderByDeliveryTime(deliveryTime);
        if (CollectionUtils.isEmpty(tmsDeliverySites)){
            return Boolean.FALSE;
        }
        for (TmsDeliverySite tmsDeliverySite : tmsDeliverySites) {
            tmsDeliverySiteMapper.deleteById(tmsDeliverySite.getId());
            log.info("补偿任务删除配送点位ID:{}", tmsDeliverySite.getId());
        }
        return Boolean.FALSE;
    }

    @Override
    public List<DeliverySiteStandardTempCondition> queryStandardItemTemperatureConditionsByIds(List<Long> deliverySiteIds) {
        if(CollectionUtils.isEmpty(deliverySiteIds)){
            return Collections.emptyList();
        }
        // 根据配送站点ID查询配送站点信息
        List<DeliverySiteStandardTempConditionDataObj> deliverySiteStandardTempConditions = tmsDeliverySiteItemMapper.queryStandardItemTemperatureConditionsByIds(deliverySiteIds);

        return DeliverySiteStandardTempConditionDataConverter.objList2DTO(deliverySiteStandardTempConditions);
    }

    @Override
    public List<DeliverySiteEntity> queryListWithItems(DeliverySiteQuery deliverySiteQuery) {
        List<TmsDeliverySite> tmsDeliverySites = tmsDeliverySiteMapper.selectList(
                new LambdaQueryWrapper<TmsDeliverySite>()
                        .in(deliverySiteQuery.getSiteIds() != null, TmsDeliverySite::getSiteId, deliverySiteQuery.getSiteIds())
                        .in(!CollectionUtils.isEmpty(deliverySiteQuery.getBatchIdList()), TmsDeliverySite::getDeliveryBatchId, deliverySiteQuery.getBatchIdList())
        );

        if(CollectionUtils.isEmpty(tmsDeliverySites)){
            return Collections.emptyList();
        }

        List<Long> deliverySiteIdList = tmsDeliverySites.stream().map(TmsDeliverySite::getId).collect(Collectors.toList());
        List<DeliverySiteEntity> deliverySiteEntities = tmsDeliverySites.stream().map(TmsDeliverySiteConverter::tmsDeliverySiteToSiteEntity).collect(Collectors.toList());

        // 查询配送详情
        List<TmsDeliverySiteItem> tmsDeliverySiteItems = tmsDeliverySiteItemMapper.queryListForceMasterByDeliverySiteId(deliverySiteIdList);

        if(CollectionUtils.isEmpty(tmsDeliverySiteItems)){
            return deliverySiteEntities;
        }
        Map<Long, List<TmsDeliverySiteItem>> deliverySiteIdItemsMap = tmsDeliverySiteItems.stream().collect(Collectors.groupingBy(TmsDeliverySiteItem::getDeliverySiteId));

        for (DeliverySiteEntity deliverySiteEntity : deliverySiteEntities) {
            List<TmsDeliverySiteItem> siteItems = deliverySiteIdItemsMap.get(deliverySiteEntity.getId());
            if(CollectionUtils.isEmpty(siteItems)){
                continue;
            }
            deliverySiteEntity.setDeliverySiteItemEntityList(siteItems.stream().map(TmsDeliverySiteItemConverter::do2Entity).collect(Collectors.toList()));

        }
        return deliverySiteEntities;
    }

    @Override
    public void batchUpdateAntSequences(List<DeliverySiteEntity> deliverySiteEntityList) {
        if(CollectionUtils.isEmpty(deliverySiteEntityList)){
            return;
        }
        List<DeliverySiteEntity> needUpdateDeliverySitesAntSequenceList = deliverySiteEntityList.stream().map(e -> {
            DeliverySiteEntity deliverySiteEntity = new DeliverySiteEntity();
            deliverySiteEntity.setId(e.getId());
            deliverySiteEntity.setAntSequence(e.getAntSequence());
            return deliverySiteEntity;
        }).collect(Collectors.toList());

        this.batchUpdate(needUpdateDeliverySitesAntSequenceList);
    }

    @Override
    public List<DeliverySiteEntity> queryForceMasterWithSiteByBatchId(Long batchId) {
        if(batchId == null){
            return Collections.emptyList();
        }
        List<TmsDeliverySite> tmsDeliverySites = tmsDeliverySiteMapper.queryForceMasterByBatchIds(Collections.singletonList(batchId));
        if(CollectionUtils.isEmpty(tmsDeliverySites)){
            return Collections.emptyList();
        }
        List<DeliverySiteEntity> deliverySiteEntities = tmsDeliverySites.stream().map(TmsDeliverySiteConverter::tmsDeliverySiteToSiteEntity).collect(Collectors.toList());

        List<Long> siteIdList = tmsDeliverySites.stream().map(TmsDeliverySite::getSiteId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(siteIdList)){
            List<TmsDistSite> tmsDistSites = tmsDistSiteMapper.selectList(
                    new LambdaQueryWrapper<TmsDistSite>().in(TmsDistSite::getId, siteIdList)
            );
            List<SiteEntity> siteEntities = tmsDistSites.stream().map(SiteConverter::tmsDistSite2Entity).collect(Collectors.toList());
            Map<Long, SiteEntity> siteIdEntityMap = siteEntities.stream().collect(Collectors.toMap(SiteEntity::getId, Function.identity()));

            for (DeliverySiteEntity deliverySiteEntity : deliverySiteEntities) {
                deliverySiteEntity.setSiteEntity(siteIdEntityMap.get(deliverySiteEntity.getSiteEntity().getId()));
            }
        }

        return deliverySiteEntities;
    }

    @Override
    public List<Long> queryCityDeliveryHaveSpecialSendSiteBatchIds(LocalDateTime deliveryTime) {
        if (deliveryTime == null) {
            return Collections.emptyList();
        }

        return tmsDeliverySiteMapper.queryCityDeliveryHaveSpecialSendSiteBatchIds(deliveryTime);
    }
}
