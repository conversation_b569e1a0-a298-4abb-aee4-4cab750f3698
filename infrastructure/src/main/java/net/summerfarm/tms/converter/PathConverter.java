package net.summerfarm.tms.converter;

import net.summerfarm.tms.base.path.entity.TmsPathEntity;
import net.summerfarm.tms.base.path.entity.TmsPathSectionEntity;
import net.summerfarm.tms.dao.TmsPath;
import net.summerfarm.tms.dao.TmsPathSection;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Description: <br/>
 * date: 2022/7/26 13:30<br/>
 *
 * <AUTHOR> />
 */
public class PathConverter {

    public static TmsPathSectionEntity section2Entity(TmsPathSection tmsPathSection) {
        TmsPathSectionEntity tmsPathSectionEntity = new TmsPathSectionEntity();
        tmsPathSectionEntity.setId(tmsPathSection.getId());
        tmsPathSectionEntity.setCreateTime(tmsPathSection.getCreateTime());
        tmsPathSectionEntity.setUpdateTime(tmsPathSection.getUpdateTime());
        tmsPathSectionEntity.setBeginSiteId(tmsPathSection.getBeginSiteId());
        tmsPathSectionEntity.setBeginSiteName(tmsPathSection.getBeginSiteName());
        tmsPathSectionEntity.setEndSiteId(tmsPathSection.getEndSiteId());
        tmsPathSectionEntity.setEndSiteName(tmsPathSection.getEndSiteName());
        tmsPathSectionEntity.setPlanSpan(tmsPathSection.getPlanSpan());
        tmsPathSectionEntity.setSequence(tmsPathSection.getSequence());
        tmsPathSectionEntity.setPathId(tmsPathSection.getPathId());
        tmsPathSectionEntity.setType(tmsPathSection.getType());
        tmsPathSectionEntity.setLoadHour(tmsPathSection.getLoadHour());
        return tmsPathSectionEntity;
    }

    public static TmsPathSection entity2SectionDo(TmsPathSectionEntity tmsPathSectionEntity, TmsPathEntity tmsPathEntity) {
        TmsPathSection tmsPathSection = new TmsPathSection();
        tmsPathSection.setId(tmsPathSectionEntity.getId());
        tmsPathSection.setBeginSiteId(tmsPathSectionEntity.getBeginSiteId());
        tmsPathSection.setBeginSiteName(tmsPathSectionEntity.getBeginSiteName());
        tmsPathSection.setEndSiteId(tmsPathSectionEntity.getEndSiteId());
        tmsPathSection.setEndSiteName(tmsPathSectionEntity.getEndSiteName());
        tmsPathSection.setPlanSpan(tmsPathSectionEntity.getPlanSpan());
        tmsPathSection.setSequence(tmsPathSectionEntity.getSequence());
        tmsPathSection.setPathId(tmsPathEntity == null ? tmsPathSectionEntity.getPathId() : tmsPathEntity.getPathId());
        tmsPathSection.setType(tmsPathSectionEntity.getType());
        tmsPathSection.setLoadHour(tmsPathSectionEntity.getLoadHour());
        return tmsPathSection;
    }

    public static TmsPathEntity do2Entity(TmsPath tmsPath) {
        if (tmsPath == null) {
            return null;
        }
        TmsPathEntity tmsPathEntity = new TmsPathEntity();
        tmsPathEntity.setPathId(tmsPath.getId());
        tmsPathEntity.setPathCode(tmsPath.getPathCode());
        tmsPathEntity.setPathName(tmsPath.getPathName());
        tmsPathEntity.setPeriodWeek(tmsPath.getPeriodWeek());
        if (!StringUtils.isEmpty(tmsPath.getPeriodWeekday())) {
            tmsPathEntity.setPeriodWeekday(
                    Stream.of(tmsPath.getPeriodWeekday().split(","))
                            .map(Integer::valueOf)
                            .collect(Collectors.toList()));
        }
        tmsPathEntity.setDeliveryDayDiff(tmsPath.getDeliveryDay());
        tmsPathEntity.setDeliveryDayTime(tmsPath.getDeliveryDayTime());
        tmsPathEntity.setCarrierId(tmsPath.getCarrierId());
        tmsPathEntity.setDriverId(tmsPath.getDriverId());
        tmsPathEntity.setCarId(tmsPath.getCarId());
        tmsPathEntity.setStatus(tmsPath.getStatus());
        tmsPathEntity.setEstimateFare(tmsPath.getEstimateFare());
        tmsPathEntity.setCreateTime(tmsPath.getCreateTime());
        tmsPathEntity.setCreator(tmsPath.getCreator());
        tmsPathEntity.setType(tmsPath.getType());
        tmsPathEntity.setBeginSiteId(tmsPath.getBeginSiteId());
        tmsPathEntity.setEndSiteId(tmsPath.getEndSiteId());
        tmsPathEntity.setRegion(tmsPath.getRegion());
        tmsPathEntity.setDistance(tmsPath.getDistance());
        return tmsPathEntity;
    }

    public static TmsPath entity2Do(TmsPathEntity tmsPathEntity) {
        TmsPath tmsPath = new TmsPath();
        tmsPath.setId(tmsPathEntity.getPathId());
        tmsPath.setCreateTime(tmsPathEntity.getCreateTime());
        tmsPath.setPathCode(tmsPathEntity.getPathCode());
        tmsPath.setPathName(tmsPathEntity.getPathName());
        tmsPath.setDriverId(tmsPathEntity.getDriverId());
        tmsPath.setCarId(tmsPathEntity.getCarId());
        tmsPath.setCarrierId(tmsPathEntity.getCarrierId());
        tmsPath.setAutoSwitch(tmsPathEntity.getAutoSwitch());
        tmsPath.setPeriodWeek(tmsPathEntity.getPeriodWeek());
        tmsPath.setDistance(tmsPathEntity.getDistance());
        tmsPath.setRegion(tmsPathEntity.getRegion());
        if (!CollectionUtils.isEmpty(tmsPathEntity.getPeriodWeekday())) {
            tmsPath.setPeriodWeekday(
                    tmsPathEntity.getPeriodWeekday().stream()
                            .map(Object::toString)
                            .collect(Collectors.joining(","))
            );
        }
        tmsPath.setDeliveryDay(tmsPathEntity.getDeliveryDayDiff());
        tmsPath.setDeliveryDayTime(tmsPathEntity.getDeliveryDayTime());
        tmsPath.setStatus(tmsPathEntity.getStatus());
        if (!CollectionUtils.isEmpty(tmsPathEntity.getTmsPathSectionList())) {
            tmsPath.setBeginSiteId(tmsPathEntity.getTmsPathSectionList().get(0).getBeginSiteId());
            tmsPath.setEndSiteId(
                    tmsPathEntity.getTmsPathSectionList()
                            .get(tmsPathEntity.getTmsPathSectionList().size() - 1)
                            .getEndSiteId()
            );
        }
        tmsPath.setCreator(tmsPathEntity.getCreator());
        tmsPath.setUpdater(tmsPathEntity.getUpdater());
        tmsPath.setCreatorId(tmsPathEntity.getCreatorId());
        tmsPath.setUpdaterId(tmsPathEntity.getUpdaterId());
        tmsPath.setEstimateFare(tmsPathEntity.getEstimateFare());
        tmsPath.setType(tmsPathEntity.getType());
        return tmsPath;
    }
}
