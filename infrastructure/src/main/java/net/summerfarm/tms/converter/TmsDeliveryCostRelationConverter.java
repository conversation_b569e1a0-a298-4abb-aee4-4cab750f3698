package net.summerfarm.tms.converter;

import net.summerfarm.tms.cost.entity.DeliveryCostRelationEntity;
import net.summerfarm.tms.dao.TmsDeliveryCostRelation;

/**
 * Description:配送成本关系PO转换器
 * date: 2023/2/15 13:46
 *
 * <AUTHOR>
 */
public class TmsDeliveryCostRelationConverter {

    public static TmsDeliveryCostRelation entity2TmsDeliveryCostRelation(DeliveryCostRelationEntity deliveryCostRelationEntity){
        TmsDeliveryCostRelation tmsDeliveryCostRelation = new TmsDeliveryCostRelation();
        if (deliveryCostRelationEntity == null){
            return tmsDeliveryCostRelation;
        }
        tmsDeliveryCostRelation.setId(deliveryCostRelationEntity.getId());
        tmsDeliveryCostRelation.setDistOrderId(deliveryCostRelationEntity.getDistOrderId());
        tmsDeliveryCostRelation.setOutItemId(deliveryCostRelationEntity.getOutItemId());
        tmsDeliveryCostRelation.setOutBatchId(deliveryCostRelationEntity.getOutBatchId());
        tmsDeliveryCostRelation.setCreateTime(deliveryCostRelationEntity.getCreateTime());
        tmsDeliveryCostRelation.setUpdateTime(deliveryCostRelationEntity.getUpdateTime());
        return tmsDeliveryCostRelation;
    }

    public static DeliveryCostRelationEntity tmsDeliveryCostRelation2Entity(TmsDeliveryCostRelation tmsDeliveryCostRelation){
        if (tmsDeliveryCostRelation == null){
            return null;
        }
        DeliveryCostRelationEntity deliveryCostRelationEntity = new DeliveryCostRelationEntity();
        deliveryCostRelationEntity.setId(tmsDeliveryCostRelation.getId());
        deliveryCostRelationEntity.setDistOrderId(tmsDeliveryCostRelation.getDistOrderId());
        deliveryCostRelationEntity.setOutItemId(tmsDeliveryCostRelation.getOutItemId());
        deliveryCostRelationEntity.setOutBatchId(tmsDeliveryCostRelation.getOutBatchId());
        deliveryCostRelationEntity.setCreateTime(tmsDeliveryCostRelation.getCreateTime());
        deliveryCostRelationEntity.setUpdateTime(tmsDeliveryCostRelation.getUpdateTime());
        return deliveryCostRelationEntity;
    }
}
