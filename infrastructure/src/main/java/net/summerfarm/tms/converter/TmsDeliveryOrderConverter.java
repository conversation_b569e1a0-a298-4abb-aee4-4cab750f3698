package net.summerfarm.tms.converter;

import net.summerfarm.tms.dao.TmsDeliveryOrder;
import net.summerfarm.tms.delivery.entity.DeliveryOrderEntity;
import net.summerfarm.tms.enums.DeliveryOrderSiteTypeEnum;
import net.summerfarm.tms.enums.DeliveryOrderStatusEnum;
import net.summerfarm.tms.enums.DistOrderSourceEnum;

/**
 * Description: <br/>
 * date: 2022/9/15 15:02<br/>
 *
 * <AUTHOR> />
 */
public class TmsDeliveryOrderConverter {

    public static DeliveryOrderEntity do2Entity(TmsDeliveryOrder tmsDeliveryOrder) {
        DeliveryOrderEntity deliveryOrderEntity = new DeliveryOrderEntity();
        if (tmsDeliveryOrder == null) {
            return deliveryOrderEntity;
        }
        deliveryOrderEntity.setId(tmsDeliveryOrder.getId());
        deliveryOrderEntity.setDistOrderId(tmsDeliveryOrder.getDistOrderId());
        deliveryOrderEntity.setDeliveryBatchId(tmsDeliveryOrder.getBatchId());
        deliveryOrderEntity.setStatus(DeliveryOrderStatusEnum.enumMap.get(tmsDeliveryOrder.getState()));
        deliveryOrderEntity.setOuterOrderId(tmsDeliveryOrder.getOutOrderId());
        deliveryOrderEntity.setOuterClientId(tmsDeliveryOrder.getOutClientId());
        deliveryOrderEntity.setOuterClientName(tmsDeliveryOrder.getOutClientName());
        deliveryOrderEntity.setOuterContactId(tmsDeliveryOrder.getOutContactId());
        deliveryOrderEntity.setBeginSiteId(tmsDeliveryOrder.getBeginSiteId());
        deliveryOrderEntity.setEndSiteId(tmsDeliveryOrder.getEndSiteId());
        deliveryOrderEntity.setDeliveryTime(tmsDeliveryOrder.getDeliveryTime());
        deliveryOrderEntity.setSource(DistOrderSourceEnum.getDistOrderSourceByCode(tmsDeliveryOrder.getSource()));
        deliveryOrderEntity.setSourceDesc(DistOrderSourceEnum.map.get(tmsDeliveryOrder.getSource()));
        deliveryOrderEntity.setDeliveryOrderSiteType(DeliveryOrderSiteTypeEnum.code2Enum(tmsDeliveryOrder.getSiteType()));
        deliveryOrderEntity.setType(tmsDeliveryOrder.getType());
        deliveryOrderEntity.setName(tmsDeliveryOrder.getName());
        deliveryOrderEntity.setPhone(tmsDeliveryOrder.getPhone());
        deliveryOrderEntity.setFinishTime(tmsDeliveryOrder.getFinishTime());

        return deliveryOrderEntity;
    }

    public static TmsDeliveryOrder entity2Do(DeliveryOrderEntity deliveryOrderEntity) {
        TmsDeliveryOrder tmsDeliveryOrder = new TmsDeliveryOrder();
        tmsDeliveryOrder.setId(deliveryOrderEntity.getId());
        tmsDeliveryOrder.setBatchId(deliveryOrderEntity.getDeliveryBatchId());
        tmsDeliveryOrder.setDistOrderId(deliveryOrderEntity.getDistOrderId());
        tmsDeliveryOrder.setSource(deliveryOrderEntity.getSource() != null ? deliveryOrderEntity.getSource().getCode() : null);
        tmsDeliveryOrder.setOutOrderId(deliveryOrderEntity.getOuterOrderId());
        tmsDeliveryOrder.setOutContactId(deliveryOrderEntity.getOuterContactId());
        tmsDeliveryOrder.setOutClientId(deliveryOrderEntity.getOuterClientId());
        tmsDeliveryOrder.setOutClientName(deliveryOrderEntity.getOuterClientName());
        tmsDeliveryOrder.setDeliveryTime(deliveryOrderEntity.getDeliveryTime());
        tmsDeliveryOrder.setBeginSiteId(deliveryOrderEntity.getBeginSiteId());
        tmsDeliveryOrder.setEndSiteId(deliveryOrderEntity.getEndSiteId());
        tmsDeliveryOrder.setState(deliveryOrderEntity.getStatus() != null ? deliveryOrderEntity.getStatus().getCode() : null);
        tmsDeliveryOrder.setSiteType(deliveryOrderEntity.getDeliveryOrderSiteType() != null ? deliveryOrderEntity.getDeliveryOrderSiteType().getCode() : null);
        tmsDeliveryOrder.setType(deliveryOrderEntity.getType());
        tmsDeliveryOrder.setName(deliveryOrderEntity.getName());
        tmsDeliveryOrder.setPhone(deliveryOrderEntity.getPhone());
        tmsDeliveryOrder.setFinishTime(deliveryOrderEntity.getFinishTime());
        tmsDeliveryOrder.setAddressDetail(deliveryOrderEntity.getAddressDetail());
        tmsDeliveryOrder.setSendRemark(deliveryOrderEntity.getSendRemark());
        return tmsDeliveryOrder;

    }
}
