package net.summerfarm.tms.converter;

import net.summerfarm.tms.after.entity.ExpenseDetailEntity;
import net.summerfarm.tms.gray.mapper.ExpenseDetail;

import java.math.RoundingMode;

/**
 * <AUTHOR>
 */
public class ExpenseDetailConverter {
    public static ExpenseDetail entity2Do(ExpenseDetailEntity source) {
        ExpenseDetail result = new ExpenseDetail();
        result.setId(source.getId());
        result.setExpenseId(source.getExpenseId());
        result.setType(source.getType());
        result.setState(source.getState());
        result.setIsReview(source.getIsReview());
        result.setPhotos(source.getPhotos());
        result.setStartAddress(source.getStartAddress());
        result.setEndAddress(source.getEndAddress());
        if(source.getMileage()!=null) {
            result.setMileage(source.getMileage().setScale(2, RoundingMode.HALF_UP));
        }
        if(source.getAmount()!=null) {
            result.setAmount(source.getAmount().setScale(2, RoundingMode.HALF_UP));
        }
        result.setRemark(source.getRemark());
        result.setUpdater(source.getUpdater());
        result.setUpdateTime(source.getUpdateTime());
        result.setCreator(source.getCreator());
        result.setCreateTime(source.getCreateTime());
        return result;
    }

    public static ExpenseDetailEntity do2Entity(ExpenseDetail source) {
        ExpenseDetailEntity result = new ExpenseDetailEntity();
        result.setId(source.getId());
        result.setExpenseId(source.getExpenseId());
        result.setType(source.getType());
        result.setState(source.getState());
        result.setIsReview(source.getIsReview());
        result.setPhotos(source.getPhotos());
        result.setStartAddress(source.getStartAddress());
        result.setEndAddress(source.getEndAddress());
        result.setMileage(source.getMileage());
        result.setAmount(source.getAmount());
        result.setRemark(source.getRemark());
        result.setUpdater(source.getUpdater());
        result.setUpdateTime(source.getUpdateTime());
        result.setCreator(source.getCreator());
        result.setCreateTime(source.getCreateTime());
        return result;
    }
}
