package net.summerfarm.tms.converter;


import com.aliyun.odps.utils.StringUtils;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.dao.TmsDeliverySite;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.enums.DeliverySiteInterceptStateEnum;
import net.summerfarm.tms.enums.DeliverySiteStatusEnum;
import net.summerfarm.tms.jsonobject.DeliverySitePropertyJson;
import net.summerfarm.tms.util.JsonUtil;

import java.util.Objects;

/**
 * Description: <br/>
 * date: 2022/9/15 15:02<br/>
 *
 * <AUTHOR> />
 */
public class TmsDeliverySiteConverter {
    public static TmsDeliverySite entity2Do(DeliverySiteEntity deliverySiteEntity) {
        TmsDeliverySite tmsDeliverySite = new TmsDeliverySite();
        tmsDeliverySite.setId(deliverySiteEntity.getId());
        tmsDeliverySite.setSiteId(deliverySiteEntity.getSiteId());
        tmsDeliverySite.setType(deliverySiteEntity.getType());
        tmsDeliverySite.setSequence(deliverySiteEntity.getSequence());
        tmsDeliverySite.setPlanArriveTime(deliverySiteEntity.getPlanArriveTime());
        tmsDeliverySite.setSignInTime(deliverySiteEntity.getSignInTime());
        tmsDeliverySite.setSignInPoi(deliverySiteEntity.getSignInPoi());
        tmsDeliverySite.setSignInDiffMinute(deliverySiteEntity.getSignInDiffMinute());
        tmsDeliverySite.setSignInDiffKm(deliverySiteEntity.getSignInDiffKm());
        tmsDeliverySite.setSignInPics(deliverySiteEntity.getSignInPics());
        tmsDeliverySite.setSignInRemark(deliverySiteEntity.getSignInRemark());
        tmsDeliverySite.setOutReason(deliverySiteEntity.getOutReason());
        tmsDeliverySite.setSignOutTime(deliverySiteEntity.getSignOutTime());
        tmsDeliverySite.setSignOutDiffKm(deliverySiteEntity.getSignOutDiffKm());
        tmsDeliverySite.setSignOutPics(deliverySiteEntity.getSignOutPics());
        tmsDeliverySite.setSignOutRemark(deliverySiteEntity.getSignOutRemark());
        tmsDeliverySite.setSignOutTemperature(deliverySiteEntity.getSignOutTemperature());
        tmsDeliverySite.setInterceptState(deliverySiteEntity.getInterceptState());
        tmsDeliverySite.setDistance(deliverySiteEntity.getDistance());
        tmsDeliverySite.setOutDistance(deliverySiteEntity.getOutDistance());
        tmsDeliverySite.setOuterClientName(deliverySiteEntity.getOuterClientName());
        tmsDeliverySite.setSignInDistance(deliverySiteEntity.getSignInDistance());
        tmsDeliverySite.setSignInStatus(deliverySiteEntity.getSignInStatus());
        tmsDeliverySite.setSignOutStatus(deliverySiteEntity.getSignOutStatus());
        tmsDeliverySite.setDeliveryBatchId(deliverySiteEntity.getDeliveryBatchId());
        tmsDeliverySite.setOuterClientId(deliverySiteEntity.getOuterClientId());
        tmsDeliverySite.setStatus(deliverySiteEntity.getStatus() != null ? deliverySiteEntity.getStatus().getCode() : null);
        tmsDeliverySite.setSignInErrType(deliverySiteEntity.getSignInErrType());
        tmsDeliverySite.setSignOutErrType(deliverySiteEntity.getSignOutErrType());
        tmsDeliverySite.setSignInAddress(deliverySiteEntity.getSignInAddress());
        tmsDeliverySite.setSignOutPoi(deliverySiteEntity.getSignOutPoi());
        tmsDeliverySite.setSignOutAddress(deliverySiteEntity.getSignOutAddress());
        tmsDeliverySite.setSendWay(deliverySiteEntity.getSendWay());
        tmsDeliverySite.setIntelligenceSequence(deliverySiteEntity.getIntelligenceSequence());
        tmsDeliverySite.setSignInSignPic(deliverySiteEntity.getSignInSignPic());
        tmsDeliverySite.setSignInProductPic(deliverySiteEntity.getSignInProductPic());
        tmsDeliverySite.setOutPic(deliverySiteEntity.getOutPic());
        tmsDeliverySite.setOutReasonType(deliverySiteEntity.getOutReasonType());
        tmsDeliverySite.setOuterBrandId(deliverySiteEntity.getOuterBrandId());
        tmsDeliverySite.setOuterBrandName(deliverySiteEntity.getOuterBrandName());
        if (Objects.nonNull(deliverySiteEntity.getSealPics()) || Objects.nonNull(deliverySiteEntity.getVehiclePlatePics())
        || Objects.nonNull(deliverySiteEntity.getRefrigeratePics()) || Objects.nonNull(deliverySiteEntity.getFreezePics())) {
            DeliverySitePropertyJson deliverySitePropertyJson = new DeliverySitePropertyJson();
            deliverySitePropertyJson.setSealPics(deliverySiteEntity.getSealPics());
            deliverySitePropertyJson.setVehiclePlatePics(deliverySiteEntity.getVehiclePlatePics());
            deliverySitePropertyJson.setRefrigeratePics(deliverySiteEntity.getRefrigeratePics());
            deliverySitePropertyJson.setFreezePics(deliverySiteEntity.getFreezePics());
            tmsDeliverySite.setPropertyJson(JsonUtil.toJson(deliverySitePropertyJson));
        }
        tmsDeliverySite.setSignOutDiffMinute(deliverySiteEntity.getSignOutDiffMinute());
        tmsDeliverySite.setSendRemark(deliverySiteEntity.getSendRemark());
        tmsDeliverySite.setTemperatureConditions(deliverySiteEntity.getTemperatureConditions());
        tmsDeliverySite.setAntSequence(deliverySiteEntity.getAntSequence());
        return tmsDeliverySite;
    }

    public static DeliverySiteEntity tmsDeliverySiteToSiteEntity(TmsDeliverySite tmsDeliverySite) {
        DeliverySiteEntity deliverySiteEntity = new DeliverySiteEntity();
        if (tmsDeliverySite == null) {
            return deliverySiteEntity;
        }

        deliverySiteEntity.setId(tmsDeliverySite.getId());
        deliverySiteEntity.setCreateTime(tmsDeliverySite.getCreateTime());
        deliverySiteEntity.setUpdateTime(tmsDeliverySite.getUpdateTime());

        deliverySiteEntity.setDeliveryBatchId(tmsDeliverySite.getDeliveryBatchId());

        SiteEntity siteEntity = new SiteEntity();
        siteEntity.setId(tmsDeliverySite.getSiteId());
        deliverySiteEntity.setSiteEntity(siteEntity);

        deliverySiteEntity.setType(tmsDeliverySite.getType());
        deliverySiteEntity.setSequence(tmsDeliverySite.getSequence());
        deliverySiteEntity.setStatus(tmsDeliverySite.getStatus() != null ? DeliverySiteStatusEnum.getDeliverySiteStatusByCode(tmsDeliverySite.getStatus()) : null);
        deliverySiteEntity.setPlanArriveTime(tmsDeliverySite.getPlanArriveTime());
        deliverySiteEntity.setSignInTime(tmsDeliverySite.getSignInTime());
        deliverySiteEntity.setSignInPoi(tmsDeliverySite.getSignInPoi());
        deliverySiteEntity.setSignInDiffMinute(tmsDeliverySite.getSignInDiffMinute());
        deliverySiteEntity.setSignInDiffKm(tmsDeliverySite.getSignInDiffKm());
        deliverySiteEntity.setSignInPics(tmsDeliverySite.getSignInPics());
        deliverySiteEntity.setSignInRemark(tmsDeliverySite.getSignInRemark());
        deliverySiteEntity.setSignInDistance(tmsDeliverySite.getSignInDistance());
        deliverySiteEntity.setOutReason(tmsDeliverySite.getOutReason());
        deliverySiteEntity.setOutDistance(tmsDeliverySite.getOutDistance());
        deliverySiteEntity.setSignOutTime(tmsDeliverySite.getSignOutTime());
        deliverySiteEntity.setSignOutDiffKm(tmsDeliverySite.getSignOutDiffKm());
        deliverySiteEntity.setSignOutPics(tmsDeliverySite.getSignOutPics());
        deliverySiteEntity.setSignOutRemark(tmsDeliverySite.getSignOutRemark());
        deliverySiteEntity.setSignOutTemperature(tmsDeliverySite.getSignOutTemperature());
        deliverySiteEntity.setDeliverySiteInterceptState(tmsDeliverySite.getInterceptState() != null ?
                DeliverySiteInterceptStateEnum.getDeliverySiteStateByCode(tmsDeliverySite.getInterceptState()) : DeliverySiteInterceptStateEnum.normal);
        deliverySiteEntity.setDistance(tmsDeliverySite.getDistance());
        deliverySiteEntity.setOuterClientName(tmsDeliverySite.getOuterClientName());
        deliverySiteEntity.setOuterClientId(tmsDeliverySite.getOuterClientId());
        deliverySiteEntity.setSiteId(tmsDeliverySite.getSiteId());
        deliverySiteEntity.setSignInStatus(tmsDeliverySite.getSignInStatus());
        deliverySiteEntity.setInterceptState(tmsDeliverySite.getInterceptState());
        deliverySiteEntity.setSignInErrType(tmsDeliverySite.getSignInErrType());
        deliverySiteEntity.setSignOutErrType(tmsDeliverySite.getSignOutErrType());
        deliverySiteEntity.setSignOutStatus(tmsDeliverySite.getSignOutStatus());
        deliverySiteEntity.setSignInAddress(tmsDeliverySite.getSignInAddress());
        deliverySiteEntity.setSignOutPoi(tmsDeliverySite.getSignOutPoi());
        deliverySiteEntity.setSignOutAddress(tmsDeliverySite.getSignOutAddress());
        deliverySiteEntity.setSendWay(tmsDeliverySite.getSendWay());
        deliverySiteEntity.setIntelligenceSequence(tmsDeliverySite.getIntelligenceSequence());
        deliverySiteEntity.setOutReasonType(tmsDeliverySite.getOutReasonType());
        deliverySiteEntity.setOutPic(tmsDeliverySite.getOutPic());
        deliverySiteEntity.setSignInSignPic(tmsDeliverySite.getSignInSignPic());
        deliverySiteEntity.setSignInProductPic(tmsDeliverySite.getSignInProductPic());
        deliverySiteEntity.setOuterBrandId(tmsDeliverySite.getOuterBrandId());
        deliverySiteEntity.setSignOutDiffMinute(tmsDeliverySite.getSignOutDiffMinute());
        deliverySiteEntity.setOuterBrandName(tmsDeliverySite.getOuterBrandName());
        if (StringUtils.isNotBlank(tmsDeliverySite.getPropertyJson())) {
            DeliverySitePropertyJson deliverySitePropertyJson = JsonUtil.toObject(tmsDeliverySite.getPropertyJson(), DeliverySitePropertyJson.class);
            deliverySiteEntity.setVehiclePlatePics(deliverySitePropertyJson.getVehiclePlatePics());
            deliverySiteEntity.setSealPics(deliverySitePropertyJson.getSealPics());
            deliverySiteEntity.setFreezePics(deliverySitePropertyJson.getFreezePics());
            deliverySiteEntity.setRefrigeratePics(deliverySitePropertyJson.getRefrigeratePics());
        }
        deliverySiteEntity.setPlanOutTime(tmsDeliverySite.getPlanOutTime());
        deliverySiteEntity.setTemperatureConditions(tmsDeliverySite.getTemperatureConditions());
        deliverySiteEntity.setSendRemark(tmsDeliverySite.getSendRemark());
        deliverySiteEntity.setAntSequence(tmsDeliverySite.getAntSequence());

        return deliverySiteEntity;
    }
}
