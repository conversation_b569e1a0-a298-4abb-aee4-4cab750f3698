package net.summerfarm.tms.converter;

import lombok.Data;
import net.summerfarm.tms.dao.TmsDeliverySiteItem;
import net.summerfarm.tms.dao.TmsDeliverySiteItemCode;
import net.summerfarm.tms.delivery.entity.DeliverySiteItemCodeEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteItemEntity;

/**
 * Description: <br/>
 * date: 2023/1/18 17:56<br/>
 *
 * <AUTHOR> />
 */
@Data
public class TmsDeliverySiteItemCodeConverter {

    public static DeliverySiteItemCodeEntity do2Entity(TmsDeliverySiteItemCode tmsDeliverySiteItemCode) {
        DeliverySiteItemCodeEntity deliverySiteItemCodeEntity = new DeliverySiteItemCodeEntity();
        if(tmsDeliverySiteItemCode == null){
            return null;
        }
        deliverySiteItemCodeEntity.setId(tmsDeliverySiteItemCode.getId());
        deliverySiteItemCodeEntity.setCreateTime(tmsDeliverySiteItemCode.getCreateTime());
        deliverySiteItemCodeEntity.setUpdateTime(tmsDeliverySiteItemCode.getUpdateTime());
        deliverySiteItemCodeEntity.setDeliverySiteItemId(tmsDeliverySiteItemCode.getDeliverySiteItemId());
        deliverySiteItemCodeEntity.setDeliverySiteId(tmsDeliverySiteItemCode.getDeliverySiteId());
        deliverySiteItemCodeEntity.setOutItemId(tmsDeliverySiteItemCode.getOutItemId());
        deliverySiteItemCodeEntity.setOnlyCode(tmsDeliverySiteItemCode.getOnlyCode());

        return deliverySiteItemCodeEntity;
    }
}
