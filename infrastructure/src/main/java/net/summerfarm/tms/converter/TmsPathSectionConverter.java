package net.summerfarm.tms.converter;

import net.summerfarm.tms.base.path.entity.TmsPathSectionEntity;
import net.summerfarm.tms.dao.TmsPathSection;

import java.util.ArrayList;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/12/20 16:19<br/>
 *
 * <AUTHOR> />
 */
public class TmsPathSectionConverter {

    public static TmsPathSectionEntity do2Entity(TmsPathSection tmsPathSection) {
        TmsPathSectionEntity tmsPathSectionEntity = new TmsPathSectionEntity();

        tmsPathSectionEntity.setId(tmsPathSection.getId());
        tmsPathSectionEntity.setCreateTime(tmsPathSection.getCreateTime());
        tmsPathSectionEntity.setUpdateTime(tmsPathSection.getUpdateTime());
        tmsPathSectionEntity.setBeginSiteId(tmsPathSection.getBeginSiteId());
        tmsPathSectionEntity.setBeginSiteName(tmsPathSection.getBeginSiteName());
        tmsPathSectionEntity.setEndSiteId(tmsPathSection.getEndSiteId());
        tmsPathSectionEntity.setEndSiteName(tmsPathSection.getEndSiteName());
        tmsPathSectionEntity.setPlanSpan(tmsPathSection.getPlanSpan());
        tmsPathSectionEntity.setSequence(tmsPathSection.getSequence());
        tmsPathSectionEntity.setPathId(tmsPathSection.getPathId());
        tmsPathSectionEntity.setType(tmsPathSection.getType());
        tmsPathSectionEntity.setLoadHour(tmsPathSection.getLoadHour());
        return tmsPathSectionEntity;
    }

    public static List<TmsPathSectionEntity> doList2EntityList(List<TmsPathSection> sectionList) {
        if (sectionList == null) {
            return null;
        }

        List<TmsPathSectionEntity> list = new ArrayList<TmsPathSectionEntity>(sectionList.size());
        for (TmsPathSection pathSection : sectionList) {
            list.add(do2Entity(pathSection));
        }
        return list;
    }

    public static TmsPathSection entity2Do(TmsPathSectionEntity tmsPathSectionEntity) {
        TmsPathSection tmsPathSection = new TmsPathSection();

        tmsPathSection.setId(tmsPathSectionEntity.getId());
        tmsPathSection.setCreateTime(tmsPathSectionEntity.getCreateTime());
        tmsPathSection.setUpdateTime(tmsPathSectionEntity.getUpdateTime());
        tmsPathSection.setBeginSiteId(tmsPathSectionEntity.getBeginSiteId());
        tmsPathSection.setBeginSiteName(tmsPathSectionEntity.getBeginSiteName());
        tmsPathSection.setEndSiteId(tmsPathSectionEntity.getEndSiteId());
        tmsPathSection.setEndSiteName(tmsPathSectionEntity.getEndSiteName());
        tmsPathSection.setPlanSpan(tmsPathSectionEntity.getPlanSpan());
        tmsPathSection.setSequence(tmsPathSectionEntity.getSequence());
        tmsPathSection.setPathId(tmsPathSectionEntity.getPathId());
        tmsPathSection.setType(tmsPathSectionEntity.getType());

        return tmsPathSection;
    }


}
