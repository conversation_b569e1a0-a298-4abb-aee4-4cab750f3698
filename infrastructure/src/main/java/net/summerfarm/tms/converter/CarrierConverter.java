package net.summerfarm.tms.converter;

import net.summerfarm.tms.base.carrier.entity.CarrierEntity;
import net.summerfarm.tms.dao.Carrier;

/**
 * Description: <br/>
 * date: 2022/7/26 13:28<br/>
 *
 * <AUTHOR> />
 */
public class CarrierConverter {

    public static CarrierEntity do2Entity(Carrier carrier) {
        if (carrier == null) {
            return null;
        }
        CarrierEntity carrierEntity = new CarrierEntity();

        carrierEntity.setId(carrier.getId());
        carrierEntity.setCreateTime(carrier.getCreateTime());
        carrierEntity.setUpdateTime(carrier.getUpdateTime());
        carrierEntity.setCarrierName(carrier.getCarrierName());
        carrierEntity.setDirector(carrier.getDirector());
        carrierEntity.setDirectorPhone(carrier.getDirectorPhone());
        carrierEntity.setAddress(carrier.getAddress());
        carrierEntity.setCooperationAgreement(carrier.getCooperationAgreement());
        carrierEntity.setBusinessType(carrier.getBusinessType());
        carrierEntity.setSubBusinessType(carrier.getSubBusinessType());
        carrierEntity.setSocialCreditCode(carrier.getSocialCreditCode());

        return carrierEntity;
    }

    public static Carrier entity2Do(CarrierEntity entity){
        if (entity == null) {
            return null;
        }
        Carrier carrier = new Carrier();

        carrier.setId(entity.getId());
        carrier.setCreateTime(entity.getCreateTime());
        carrier.setUpdateTime(entity.getUpdateTime());
        carrier.setCarrierName(entity.getCarrierName());
        carrier.setDirector(entity.getDirector());
        carrier.setDirectorPhone(entity.getDirectorPhone());
        carrier.setAddress(entity.getAddress());
        carrier.setCooperationAgreement(entity.getCooperationAgreement());
        carrier.setBusinessType(entity.getBusinessType());
        carrier.setSubBusinessType(entity.getSubBusinessType());
        carrier.setSocialCreditCode(entity.getSocialCreditCode());

        return carrier;
    }
}
