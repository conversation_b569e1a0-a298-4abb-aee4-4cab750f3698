package net.summerfarm.tms.converter;

import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.dao.TmsDeliverySection;
import net.summerfarm.tms.delivery.entity.DeliverySectionEntity;

/**
 * Description: <br/>
 * date: 2022/12/8 14:19<br/>
 *
 * <AUTHOR> />
 */
public class TmsDeliverySectionConverter {

    public static TmsDeliverySection entity2Do(DeliverySectionEntity deliverySectionEntity) {
        TmsDeliverySection tmsDeliverySection = new TmsDeliverySection();

        tmsDeliverySection.setId(deliverySectionEntity.getId());
        tmsDeliverySection.setBatchId(deliverySectionEntity.getBatchId());
        tmsDeliverySection.setBeginSiteId(deliverySectionEntity.getBeginSiteEntity().getId());
        tmsDeliverySection.setEndSiteId(deliverySectionEntity.getEndSiteEntity().getId());
        tmsDeliverySection.setDistance(deliverySectionEntity.getDistance());
        tmsDeliverySection.setType(deliverySectionEntity.getType());

        return tmsDeliverySection;
    }

    public static DeliverySectionEntity do2Entity(TmsDeliverySection tmsDeliverySection) {
        DeliverySectionEntity deliverySectionEntity = new DeliverySectionEntity();

        deliverySectionEntity.setId(tmsDeliverySection.getId());
        deliverySectionEntity.setBatchId(tmsDeliverySection.getBatchId());
        deliverySectionEntity.setBeginSiteEntity(SiteEntity.builder().id(tmsDeliverySection.getBeginSiteId()).build());
        deliverySectionEntity.setEndSiteEntity(SiteEntity.builder().id(tmsDeliverySection.getEndSiteId()).build());
        deliverySectionEntity.setDistance(tmsDeliverySection.getDistance());
        deliverySectionEntity.setType(tmsDeliverySection.getType());

        return deliverySectionEntity;
    }
}
