package net.summerfarm.tms.converter;

import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.dao.TmsDeliveryOrder;
import net.summerfarm.tms.dao.TmsDistItem;
import net.summerfarm.tms.dao.TmsDistOrder;
import net.summerfarm.tms.delivery.entity.DeliveryOrderEntity;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.vo.DistClientVO;
import net.summerfarm.tms.dist.vo.DistFlowVO;
import net.summerfarm.tms.dist.vo.DistItemVO;
import net.summerfarm.tms.enums.DistOrderCancelTypeEnum;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.enums.DistOrderStatusEnum;
import net.summerfarm.tms.enums.DistPickTypeEnum;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:
 * date: 2022/9/14 17:20
 *
 * <AUTHOR>
 */
public class TmsDistOrderConverter {

    public static DistOrderEntity tmsDistOrder2Entity(TmsDistOrder tmsDistOrder, List<TmsDistItem> items, List<TmsDeliveryOrder> deliveryOrders) {
        if (tmsDistOrder == null) {
            return null;
        }
        DistOrderEntity distOrderEntity = tmsDistOrder2Entity(tmsDistOrder, items);
        List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrders.stream().map(TmsDeliveryOrderConverter::do2Entity).collect(Collectors.toList());
        distOrderEntity.setDeliveryOrders(deliveryOrderEntities);
        return distOrderEntity;
    }

    public static DistOrderEntity tmsDistOrder2Entity(TmsDistOrder tmsDistOrder, List<TmsDistItem> items) {
        if (tmsDistOrder == null) {
            return null;
        }
        DistOrderEntity distOrderEntity = tmsDistOrder2Entity(tmsDistOrder);
        List<DistItemVO> itemVos = items.stream().map(TmsDistItemConverter::tmsDistItem2Vo).collect(Collectors.toList());
        distOrderEntity.setDistItems(itemVos);
        distOrderEntity.orderItemsStats();
        return distOrderEntity;
    }

    public static DistOrderEntity tmsDistOrder2Entity(TmsDistOrder tmsDistOrder) {
        if (tmsDistOrder == null) {
            return null;
        }
        DistOrderEntity distOrderEntity = new DistOrderEntity();
        distOrderEntity.setDistId(tmsDistOrder.getId());
        //获取委托单状态实体
        DistOrderStatusEnum distOrderStatusEnum = DistOrderStatusEnum.getDistOrderStatusByCode(tmsDistOrder.getState());
        distOrderEntity.setStatus(distOrderStatusEnum);
        //获取委托单来源实体
        DistOrderSourceEnum distOrderSourceEnum = DistOrderSourceEnum.getDistOrderSourceByCode(tmsDistOrder.getSource());
        distOrderEntity.setSource(distOrderSourceEnum);
        //获取委托单拣货类型实体
        DistPickTypeEnum distPickTypeEnum = DistPickTypeEnum.getDistOrderPickTypeByCode(tmsDistOrder.getPickType());
        distOrderEntity.setPickType(distPickTypeEnum);
        distOrderEntity.setCloseReason(tmsDistOrder.getOuterRemark());
        //客户信息
        distOrderEntity.setDistClientVO(getDistClientVO(tmsDistOrder));
        //配送信息
        distOrderEntity.setDistFlowVO(getDistFlowVO(tmsDistOrder));
        //点位信息
        SiteEntity beginSite = new SiteEntity();
        beginSite.setId(tmsDistOrder.getBeginSiteId());
        SiteEntity midSite = new SiteEntity();
        midSite.setId(tmsDistOrder.getMidSiteId());
        SiteEntity endSite = new SiteEntity();
        endSite.setId(tmsDistOrder.getEndSiteId());
        distOrderEntity.setBeginSite(beginSite);
        distOrderEntity.setMidSite(midSite);
        distOrderEntity.setEndSite(endSite);
        //取消委托单相关
        distOrderEntity.setCancelTime(tmsDistOrder.getCancelTime());
        if(tmsDistOrder.getCancelType() != null){
            DistOrderCancelTypeEnum cancelTypeEnum = DistOrderCancelTypeEnum.getCancelTypeByCode(tmsDistOrder.getCancelType());
            distOrderEntity.setCancelType(cancelTypeEnum);
        }
        //操作人信息
        distOrderEntity.setCreator(tmsDistOrder.getCreator());
        distOrderEntity.setCreatorId(tmsDistOrder.getCreatorId());
        distOrderEntity.setUpdater(tmsDistOrder.getUpdater());
        distOrderEntity.setUpdaterId(tmsDistOrder.getUpdaterId());
        distOrderEntity.setCreateTime(tmsDistOrder.getCreateTime());
        distOrderEntity.setUpdateTime(tmsDistOrder.getUpdateTime());
        distOrderEntity.setSendRemark(tmsDistOrder.getSendRemark());
        distOrderEntity.setBeginClientName(tmsDistOrder.getBeginClientName());
        distOrderEntity.setFulfillmentDeliveryWay(tmsDistOrder.getFulfillmentDeliveryWay());
        return distOrderEntity;
    }

    private static DistClientVO getDistClientVO(TmsDistOrder tmsDistOrder) {
        DistClientVO distClientVO = new DistClientVO();
        distClientVO.setOutOrderId(tmsDistOrder.getOuterOrderId());
        distClientVO.setOutTenantId(tmsDistOrder.getOuterTenantId());
        distClientVO.setOutBrandName(tmsDistOrder.getOuterBrandName());
        distClientVO.setOutClientId(tmsDistOrder.getOuterClientId());
        distClientVO.setOutClientName(tmsDistOrder.getOuterClientName());
        distClientVO.setOutContactId(tmsDistOrder.getOuterContactId());
        return distClientVO;
    }

    private static DistFlowVO getDistFlowVO(TmsDistOrder tmsDistOrder) {
        DistFlowVO distFlowVO = new DistFlowVO();
        distFlowVO.setExpectBeginTime(tmsDistOrder.getExpectBeginTime());
        distFlowVO.setExpectEndTime(tmsDistOrder.getExpectEndTime());
        distFlowVO.setType(tmsDistOrder.getType());
        distFlowVO.setRealArrivalTime(tmsDistOrder.getRealArrivalTime());
        distFlowVO.setTimeFrame(tmsDistOrder.getTimeFrame());
        return distFlowVO;
    }

    public static TmsDistOrder entity2TmsDistOrder(DistOrderEntity distOrderEntity) {
        TmsDistOrder tmsDistOrder = new TmsDistOrder();
        if (distOrderEntity == null) {
            return tmsDistOrder;
        }
        tmsDistOrder.setId(distOrderEntity.getDistId());
        tmsDistOrder.setState(distOrderEntity.getStatus() != null ? distOrderEntity.getStatus().getCode() : null);
        tmsDistOrder.setSource(distOrderEntity.getSource() != null ? distOrderEntity.getSource().getCode() : null);
        tmsDistOrder.setPickType(distOrderEntity.getPickType() != null ? distOrderEntity.getPickType().getCode() : null);
        tmsDistOrder.setOuterRemark(distOrderEntity.getCloseReason());

        DistClientVO distClientVO = distOrderEntity.getDistClientVO();
        if(distClientVO != null){
            tmsDistOrder.setOuterOrderId(distClientVO.getOutOrderId());
            tmsDistOrder.setOuterTenantId(distClientVO.getOutTenantId());
            tmsDistOrder.setOuterBrandName(distClientVO.getOutBrandName());
            tmsDistOrder.setOuterClientId(distClientVO.getOutClientId());
            tmsDistOrder.setOuterClientName(distClientVO.getOutClientName());
            tmsDistOrder.setOuterContactId(distClientVO.getOutContactId());
        }

        DistFlowVO distFlowVO = distOrderEntity.getDistFlowVO();
        if(distFlowVO != null){
            tmsDistOrder.setType(distFlowVO.getType());
            tmsDistOrder.setExpectBeginTime(distFlowVO.getExpectBeginTime());
            tmsDistOrder.setExpectEndTime(distFlowVO.getExpectEndTime());
            tmsDistOrder.setRealArrivalTime(distFlowVO.getRealArrivalTime());
            tmsDistOrder.setTimeFrame(distFlowVO.getTimeFrame());
        }

        SiteEntity beginSite = distOrderEntity.getBeginSite();
        SiteEntity midSite = distOrderEntity.getMidSite();
        SiteEntity endSite = distOrderEntity.getEndSite();
        tmsDistOrder.setBeginSiteId(beginSite != null ? beginSite.getId() : null);
        if (midSite != null) {
            tmsDistOrder.setMidSiteId(midSite.getId());
        }
        tmsDistOrder.setEndSiteId(endSite != null ? endSite.getId() : null);

        tmsDistOrder.setCreatorId(distOrderEntity.getCreatorId());
        tmsDistOrder.setCreator(distOrderEntity.getCreator());
        tmsDistOrder.setCreateTime(distOrderEntity.getCreateTime());
        tmsDistOrder.setUpdaterId(distOrderEntity.getUpdaterId());
        tmsDistOrder.setUpdater(distOrderEntity.getUpdater());
        tmsDistOrder.setUpdateTime(distOrderEntity.getUpdateTime());
        tmsDistOrder.setCancelTime(distOrderEntity.getCancelTime());
        tmsDistOrder.setCancelType(distOrderEntity.getCancelType() != null ? distOrderEntity.getCancelType().getCode() : null);
        tmsDistOrder.setSendRemark(distOrderEntity.getSendRemark());
        tmsDistOrder.setBeginClientName(distOrderEntity.getBeginClientName());
        tmsDistOrder.setFulfillmentDeliveryWay(distOrderEntity.getFulfillmentDeliveryWay());

        return tmsDistOrder;
    }

}
