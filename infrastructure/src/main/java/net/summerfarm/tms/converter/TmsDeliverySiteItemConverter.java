package net.summerfarm.tms.converter;


import com.baomidou.mybatisplus.annotation.TableField;
import net.summerfarm.tms.dao.TmsDeliverySiteItem;
import net.summerfarm.tms.delivery.entity.DeliverySiteItemEntity;

/**
 * Description: <br/>
 * date: 2022/9/15 15:02<br/>
 *
 * <AUTHOR> />
 */
public class TmsDeliverySiteItemConverter {
    public static TmsDeliverySiteItem entity2Do(DeliverySiteItemEntity source) {
        if(source == null){
            return null;
        }
        TmsDeliverySiteItem result = new TmsDeliverySiteItem();
        result.setId(source.getId());
        result.setCreateTime(source.getCreateTime());
        result.setUpdateTime(source.getUpdateTime());
        result.setDeliverySiteId(source.getDeliverySiteId());
        result.setOutItemId(source.getOutItemId());
        result.setPlanReceiptCount(source.getPlanReceiptCount());
        result.setRealReceiptCount(source.getRealReceiptCount());
        result.setShortCount(source.getShortCount());
        result.setInterceptCount(source.getInterceptCount());
        result.setRejectCount(source.getRejectCount());
        result.setRejectReason(source.getRejectReason());
        result.setScanCount(source.getScanCount());
        result.setNoscanCount(source.getNoscanCount());
        result.setNoscanReason(source.getNoscanReason());
        result.setNoscanPics(source.getNoscanPics());
        result.setType(source.getType());
        result.setRemark(source.getRemark());
        result.setOutItemName(source.getOutItemName());
        result.setOutItemType(source.getOutItemType());
        result.setStatus(source.getStatus());
        result.setPackType(source.getPackType());
        result.setTemperature(source.getTemperature());
        return result;
    }

    public static DeliverySiteItemEntity do2Entity(TmsDeliverySiteItem source) {
        if(source == null){
            return null;
        }
        DeliverySiteItemEntity result = new DeliverySiteItemEntity();
        result.setId(source.getId());
        result.setCreateTime(source.getCreateTime());
        result.setUpdateTime(source.getUpdateTime());
        result.setDeliverySiteId(source.getDeliverySiteId());
        result.setOutItemId(source.getOutItemId());
        result.setPlanReceiptCount(source.getPlanReceiptCount());
        result.setRealReceiptCount(source.getRealReceiptCount());
        result.setShortCount(source.getShortCount());
        result.setInterceptCount(source.getInterceptCount());
        result.setRejectCount(source.getRejectCount());
        result.setRejectReason(source.getRejectReason());
        result.setScanCount(source.getScanCount());
        result.setNoscanCount(source.getNoscanCount());
        result.setNoscanReason(source.getNoscanReason());
        result.setNoscanPics(source.getNoscanPics());
        result.setType(source.getType());
        result.setRemark(source.getRemark());
        result.setOutItemName(source.getOutItemName());
        result.setOutItemType(source.getOutItemType());
        result.setStatus(source.getStatus());
        result.setPackType(source.getPackType());
        result.setTemperature(source.getTemperature());
        return result;
    }
}
