package net.summerfarm.tms.converter;

import net.summerfarm.tms.dao.TmsDistItem;
import net.summerfarm.tms.dist.vo.DistItemVO;
import net.summerfarm.tms.enums.DistItemDeliveryTypeEnum;
import net.summerfarm.tms.enums.DistItemTypeEnum;
import net.summerfarm.tms.enums.TmsTemperatureEnum;

import java.util.Objects;

/**
 * Description:
 * date: 2022/9/14 18:51
 *
 * <AUTHOR>
 */
public class TmsDistItemConverter {

    public static DistItemVO tmsDistItem2Vo(TmsDistItem tmsDistItem) {
        DistItemVO distItemVO = new DistItemVO();
        if (tmsDistItem == null) {
            return distItemVO;
        }
        distItemVO.setId(tmsDistItem.getId());
        distItemVO.setDistOrderId(tmsDistItem.getDistOrderId());
        distItemVO.setOutItemId(tmsDistItem.getOuterItemId());
        distItemVO.setOutItemType(tmsDistItem.getOuterItemType());
        distItemVO.setOutItemName(tmsDistItem.getOuterItemName());
        distItemVO.setOutItemPrice(tmsDistItem.getOuterItemPrice());
        distItemVO.setVolume(tmsDistItem.getVolume());
        distItemVO.setWeight(tmsDistItem.getWeight());
        distItemVO.setQuantity(tmsDistItem.getQuantity());
        //获取温区枚举
        TmsTemperatureEnum temperatureEnum = TmsTemperatureEnum.getTemperatureByCode(tmsDistItem.getTemperature());
        distItemVO.setTemperatureEnum(temperatureEnum);
        distItemVO.setSpecification(tmsDistItem.getSpecification());
        distItemVO.setUnit(tmsDistItem.getUnit());
        //获取物品类型枚举
        DistItemTypeEnum distItemTypeEnum = DistItemTypeEnum.getDistItemTypeByCode(tmsDistItem.getType());
        distItemVO.setItemTypeEnum(distItemTypeEnum);
        //获取物品配送类型枚举
        DistItemDeliveryTypeEnum distItemDeliveryTypeEnum = DistItemDeliveryTypeEnum.getDistItemDeliveryTypeByCode(tmsDistItem.getDeliveryType());
        distItemVO.setItemDeliveryTypeEnum(distItemDeliveryTypeEnum);
        distItemVO.setCreateTime(tmsDistItem.getCreateTime());
        distItemVO.setUpdateTime(tmsDistItem.getUpdateTime());
        distItemVO.setDeliveryType(tmsDistItem.getDeliveryType());
        distItemVO.setType(tmsDistItem.getType());
        distItemVO.setPackType(tmsDistItem.getPackType());
        return distItemVO;
    }

    public static TmsDistItem vo2TmsDistItem(DistItemVO distItemVO) {
        TmsDistItem tmsDistItem = new TmsDistItem();
        if (distItemVO == null) {
            return tmsDistItem;
        }
        tmsDistItem.setId(distItemVO.getId());
        tmsDistItem.setCreateTime(distItemVO.getCreateTime());
        tmsDistItem.setUpdateTime(distItemVO.getUpdateTime());
        tmsDistItem.setOuterItemId(distItemVO.getOutItemId());
        tmsDistItem.setOuterItemType(distItemVO.getOutItemType());
        tmsDistItem.setOuterItemName(distItemVO.getOutItemName());
        tmsDistItem.setOuterItemPrice(distItemVO.getOutItemPrice());
        tmsDistItem.setDistOrderId(distItemVO.getDistOrderId());
        tmsDistItem.setVolume(distItemVO.getVolume());
        tmsDistItem.setWeight(distItemVO.getWeight());
        tmsDistItem.setQuantity(distItemVO.getQuantity());
        tmsDistItem.setTemperature(distItemVO.getTemperatureEnum().getCode());
        tmsDistItem.setSpecification(distItemVO.getSpecification());
        tmsDistItem.setUnit(distItemVO.getUnit());
        tmsDistItem.setType(distItemVO.getItemTypeEnum().getCode());
        if (distItemVO.getItemDeliveryTypeEnum() != null) {
            tmsDistItem.setDeliveryType(distItemVO.getItemDeliveryTypeEnum().getCode());
        }
        tmsDistItem.setPackType(distItemVO.getPackType());
        return tmsDistItem;
    }
    

}
