package net.summerfarm.tms.converter;

import net.summerfarm.tms.cost.entity.DeliveryCostEntity;
import net.summerfarm.tms.dao.TmsDeliveryCost;
import net.summerfarm.tms.enums.DistOrderSourceEnum;

/**
 * Description:配送成本PO转换器
 * date: 2023/2/10 18:38
 *
 * <AUTHOR>
 */
public class TmsDeliveryCostConverter {

    public static TmsDeliveryCost entity2TmsDeliveryCost(DeliveryCostEntity deliveryCostEntity){
        TmsDeliveryCost tmsDeliveryCost = new TmsDeliveryCost();
        if (deliveryCostEntity == null){
            return tmsDeliveryCost;
        }
        tmsDeliveryCost.setId(deliveryCostEntity.getId());
        tmsDeliveryCost.setDistOrderId(deliveryCostEntity.getDistOrderId());
        tmsDeliveryCost.setOutItemId(deliveryCostEntity.getOutItemId());
        tmsDeliveryCost.setSource(deliveryCostEntity.getSource().getCode());
        tmsDeliveryCost.setQuantity(deliveryCostEntity.getQuantity());
        tmsDeliveryCost.setAverageCost(deliveryCostEntity.getAverageCost());
        tmsDeliveryCost.setCreateTime(deliveryCostEntity.getCreateTime());
        tmsDeliveryCost.setUpdateTime(deliveryCostEntity.getUpdateTime());
        return tmsDeliveryCost;
    }

    public static DeliveryCostEntity tmsDeliveryCost2Entity(TmsDeliveryCost tmsDeliveryCost){
        if (tmsDeliveryCost == null){
            return null;
        }
        DeliveryCostEntity deliveryCostEntity = new DeliveryCostEntity();
        deliveryCostEntity.setId(tmsDeliveryCost.getId());
        deliveryCostEntity.setDistOrderId(tmsDeliveryCost.getDistOrderId());
        deliveryCostEntity.setOutItemId(tmsDeliveryCost.getOutItemId());
        //获取委托单来源实体
        DistOrderSourceEnum distOrderSourceEnum = DistOrderSourceEnum.getDistOrderSourceByCode(tmsDeliveryCost.getSource());
        deliveryCostEntity.setSource(distOrderSourceEnum);
        deliveryCostEntity.setQuantity(tmsDeliveryCost.getQuantity());
        deliveryCostEntity.setAverageCost(tmsDeliveryCost.getAverageCost());
        deliveryCostEntity.setCreateTime(tmsDeliveryCost.getCreateTime());
        deliveryCostEntity.setUpdateTime(tmsDeliveryCost.getUpdateTime());
        return deliveryCostEntity;
    }
}
