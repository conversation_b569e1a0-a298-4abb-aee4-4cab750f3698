package net.summerfarm.tms.converter;

import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.dao.TmsDistSite;

import java.util.Objects;

/**
 * Description: <br/>
 * date: 2022/7/26 13:30<br/>
 *
 * <AUTHOR> />
 */
public class SiteConverter {

    public static SiteEntity tmsDistSite2Entity(TmsDistSite tmsDistSite) {
        SiteEntity siteEntity = new SiteEntity();
        if (tmsDistSite == null) {
            return null;
        }

        siteEntity.setId(tmsDistSite.getId());
        siteEntity.setProvince(tmsDistSite.getProvice());
        siteEntity.setCity(tmsDistSite.getCity());
        siteEntity.setArea(tmsDistSite.getArea());
        siteEntity.setAddress(tmsDistSite.getAddress());
        siteEntity.setPoi(tmsDistSite.getPoi());
        siteEntity.setPhone(tmsDistSite.getPhone());
        siteEntity.setName(tmsDistSite.getName());
        siteEntity.setType(tmsDistSite.getType());
        siteEntity.setState(tmsDistSite.getState());
        siteEntity.setPunchDistance(tmsDistSite.getPunchDistance());
        siteEntity.setOutBusinessNo(tmsDistSite.getOutBusinessNo());
        siteEntity.setOutTime(tmsDistSite.getOutTime());
        siteEntity.setCreateTime(tmsDistSite.getCreateTime());
        siteEntity.setUpdateTime(tmsDistSite.getUpdateTime());
        siteEntity.setContactPerson(tmsDistSite.getContactPerson());
        siteEntity.setSuperviseSiteId(tmsDistSite.getSuperviseSiteId());
        siteEntity.setCreator(tmsDistSite.getCreator());
        siteEntity.setIntelligencePath(tmsDistSite.getIntelligencePath());
        siteEntity.setRegion(tmsDistSite.getRegion());
        siteEntity.setSitePics(tmsDistSite.getSitePics());
        siteEntity.setSiteUse(tmsDistSite.getSiteUse());

        return siteEntity;
    }

    public static TmsDistSite entity2tmsDistSite(SiteEntity siteEntity) {
        TmsDistSite tmsDistSite = new TmsDistSite();

        tmsDistSite.setId(siteEntity.getId());
        tmsDistSite.setOutBusinessNo(siteEntity.getOutBusinessNo());
        tmsDistSite.setProvice(siteEntity.getProvince());
        tmsDistSite.setCity(siteEntity.getCity());
        tmsDistSite.setArea(siteEntity.getArea());
        tmsDistSite.setAddress(siteEntity.getAddress());
        tmsDistSite.setPoi(siteEntity.getPoi());
        tmsDistSite.setPhone(siteEntity.getPhone());
        tmsDistSite.setName(siteEntity.getName());
        tmsDistSite.setType(siteEntity.getType());
        tmsDistSite.setState(siteEntity.getState());
        tmsDistSite.setPunchDistance(siteEntity.getPunchDistance());
        tmsDistSite.setOutBusinessNo(siteEntity.getOutBusinessNo());
        tmsDistSite.setOutTime(siteEntity.getOutTime());
        tmsDistSite.setCreateTime(siteEntity.getCreateTime());
        tmsDistSite.setUpdateTime(siteEntity.getUpdateTime());
        tmsDistSite.setContactPerson(siteEntity.getContactPerson());
        tmsDistSite.setSuperviseSiteId(siteEntity.getSuperviseSiteId());
        tmsDistSite.setCreator(siteEntity.getCreator());
        tmsDistSite.setIntelligencePath(siteEntity.getIntelligencePath());
        tmsDistSite.setSitePics(siteEntity.getSitePics());
        tmsDistSite.setSiteUse(siteEntity.getSiteUse());

        return tmsDistSite;
    }
    
}
