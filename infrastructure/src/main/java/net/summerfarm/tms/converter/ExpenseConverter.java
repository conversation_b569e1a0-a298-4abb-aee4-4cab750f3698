package net.summerfarm.tms.converter;

import net.summerfarm.tms.after.entity.ExpenseEntity;
import net.summerfarm.tms.gray.mapper.Expense;

/**
 * <AUTHOR>
 */
public class ExpenseConverter {
    public static Expense entity2Do(ExpenseEntity source) {
        Expense result = new Expense();
        result.setId(source.getId());
        result.setDriverId(source.getDriverId());
        result.setDeliveryPathId(source.getDeliveryPathId());
        result.setDeliveryTime(source.getDeliveryTime());
        result.setType(source.getType());
        result.setState(source.getState());
        result.setStoreNo(source.getStoreNo());
        result.setIsReview(source.getIsReview());
        result.setMId(source.getMId());
        result.setMname(source.getMname());
        result.setUpdater(source.getUpdater());
        result.setUpdateTime(source.getUpdateTime());
        result.setCreateTime(source.getCreateTime());
        result.setCreator(source.getCreator());
        result.setStatus(source.getStatus());
        result.setReason(source.getReason());
        result.setTmsDeliverySiteId(source.getTmsDeliverySiteId());
        return result;
    }

    public static ExpenseEntity do2Entity(Expense source) {
        ExpenseEntity result = new ExpenseEntity();
        if(source == null){
            return result;
        }
        result.setId(source.getId());
        result.setDriverId(source.getDriverId());
        result.setDeliveryPathId(source.getDeliveryPathId());
        result.setDeliveryTime(source.getDeliveryTime());
        result.setType(source.getType());
        result.setState(source.getState());
        result.setStoreNo(source.getStoreNo());
        result.setIsReview(source.getIsReview());
        result.setMId(source.getMId());
        result.setMname(source.getMname());
        result.setUpdater(source.getUpdater());
        result.setUpdateTime(source.getUpdateTime());
        result.setCreateTime(source.getCreateTime());
        result.setCreator(source.getCreator());
        result.setStatus(source.getStatus());
        result.setReason(source.getReason());
        result.setTmsDeliverySiteId(source.getTmsDeliverySiteId());
        return result;
    }
}
