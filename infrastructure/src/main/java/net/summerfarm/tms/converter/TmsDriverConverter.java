package net.summerfarm.tms.converter;

import net.summerfarm.tms.base.driver.entity.DriverEntity;
import net.summerfarm.tms.dao.TmsDriver;

import java.io.Serializable;

/**
 * Description: <br/>
 * date: 2022/7/14 14:48<br/>
 *
 * <AUTHOR> />
 */
public class TmsDriverConverter implements Serializable {

    public static DriverEntity tmsDriver2Entity(TmsDriver tmsDriver) {
        DriverEntity driverEntity = new DriverEntity();

        if (tmsDriver == null) {
            return driverEntity;
        }

        driverEntity.setId(tmsDriver.getId());
        driverEntity.setName(tmsDriver.getName());
        driverEntity.setPhone(tmsDriver.getPhone());
        driverEntity.setCooperationCycle(tmsDriver.getCooperationCycle());
        driverEntity.setBusinessType(tmsDriver.getBusinessType());
        driverEntity.setStatus(tmsDriver.getStatus());
        driverEntity.setIdCard(tmsDriver.getIdCard());
        driverEntity.setIdCardFrontPic(tmsDriver.getIdCardFrontPic());
        driverEntity.setIdCardBehindPic(tmsDriver.getIdCardBehindPic());
        driverEntity.setDriverPics(tmsDriver.getDriverPics());
        driverEntity.setCreateTime(tmsDriver.getCreateTime());
        driverEntity.setUpdateTime(tmsDriver.getUpdateTime());
        driverEntity.setAdminId(tmsDriver.getAdminId());
        driverEntity.setCityWarehouseSiteId(tmsDriver.getCitySiteId());
        driverEntity.setCityCarrierId(tmsDriver.getCityCarrierId());
        driverEntity.setBaseUserId(tmsDriver.getBaseUserId());
        driverEntity.setKeepTemperatureMethodAudit(tmsDriver.getKeepTemperatureMethodAudit());

        return driverEntity;
    }

    public static TmsDriver entity2TmsDriver(DriverEntity driverEntity) {
        TmsDriver tmsDriver = new TmsDriver();

        tmsDriver.setId(driverEntity.getId());
        tmsDriver.setName(driverEntity.getName());
        tmsDriver.setPhone(driverEntity.getPhone());
        tmsDriver.setCooperationCycle(driverEntity.getCooperationCycle());
        tmsDriver.setBusinessType(driverEntity.getBusinessType());
        tmsDriver.setStatus(driverEntity.getStatus());
        tmsDriver.setIdCard(driverEntity.getIdCard());
        tmsDriver.setIdCardFrontPic(driverEntity.getIdCardFrontPic());
        tmsDriver.setIdCardBehindPic(driverEntity.getIdCardBehindPic());
        tmsDriver.setDriverPics(driverEntity.getDriverPics());
        tmsDriver.setAdminId(driverEntity.getAdminId());
        tmsDriver.setCitySiteId(driverEntity.getCityWarehouseSiteId());
        tmsDriver.setCityCarrierId(driverEntity.getCityCarrierId());
        tmsDriver.setBaseUserId(driverEntity.getBaseUserId());
        tmsDriver.setKeepTemperatureMethodAudit(driverEntity.getKeepTemperatureMethodAudit());

        return tmsDriver;
    }

}
