package net.summerfarm.tms.converter;

import net.summerfarm.tms.dao.TmsLackGoodsApproved;
import net.summerfarm.tms.lack.entity.LackApprovedEntity;

/**
 * Description: <br/>
 * date: 2022/11/10 15:33<br/>
 *
 * <AUTHOR> />
 */
public class TmsLackGoodsApprovedConverter {

    public static LackApprovedEntity do2Entity(TmsLackGoodsApproved tmsLackGoodsApproved) {
        if(tmsLackGoodsApproved == null){
            return null;
        }
        LackApprovedEntity lackApprovedEntity = new LackApprovedEntity();
        lackApprovedEntity.setId(tmsLackGoodsApproved.getId());
        lackApprovedEntity.setStoreNo(tmsLackGoodsApproved.getStoreNo());
        lackApprovedEntity.setWarehouseNo(tmsLackGoodsApproved.getWarehouseNo());
        lackApprovedEntity.setSku(tmsLackGoodsApproved.getSku());
        lackApprovedEntity.setMId(tmsLackGoodsApproved.getMId());
        lackApprovedEntity.setLackNum(tmsLackGoodsApproved.getLackNum());
        lackApprovedEntity.setMoney(tmsLackGoodsApproved.getMoney());
        lackApprovedEntity.setLackType(tmsLackGoodsApproved.getLackType());
        lackApprovedEntity.setRemark(tmsLackGoodsApproved.getRemark());
        lackApprovedEntity.setResponsible(tmsLackGoodsApproved.getResponsible());
        lackApprovedEntity.setState(tmsLackGoodsApproved.getState());
        lackApprovedEntity.setBuyOut(tmsLackGoodsApproved.getBuyOut());
        lackApprovedEntity.setBuyOutMoney(tmsLackGoodsApproved.getBuyOutMoney());
        lackApprovedEntity.setJudgmentOpinion(tmsLackGoodsApproved.getJudgmentOpinion());
        lackApprovedEntity.setStockTaskId(tmsLackGoodsApproved.getStockTaskId());
        lackApprovedEntity.setPic(tmsLackGoodsApproved.getPic());
        lackApprovedEntity.setCreateTime(tmsLackGoodsApproved.getCreateTime());
        lackApprovedEntity.setUpdateTime(tmsLackGoodsApproved.getUpdateTime());
        lackApprovedEntity.setApprovedAdminId(tmsLackGoodsApproved.getApprovedAdminId());
        lackApprovedEntity.setApprovedTime(tmsLackGoodsApproved.getApprovedTime());
        lackApprovedEntity.setResponsibilityAdminId(tmsLackGoodsApproved.getResponsibilityAdminId());
        lackApprovedEntity.setResponsibilityTime(tmsLackGoodsApproved.getResponsibilityTime());
        lackApprovedEntity.setFinishTime(tmsLackGoodsApproved.getFinishTime());
        lackApprovedEntity.setOrderNo(tmsLackGoodsApproved.getOrderNo());
        lackApprovedEntity.setAmount(tmsLackGoodsApproved.getAmount());
        lackApprovedEntity.setStockLackNum(tmsLackGoodsApproved.getStockLackNum());
        lackApprovedEntity.setTmsDeliverySiteId(tmsLackGoodsApproved.getTmsDeliverySiteId());
        lackApprovedEntity.setApprovedName(tmsLackGoodsApproved.getApprovedName());
        lackApprovedEntity.setResponsibilityName(tmsLackGoodsApproved.getResponsibilityName());
        lackApprovedEntity.setAppealFlag(tmsLackGoodsApproved.getAppealFlag());
        lackApprovedEntity.setResponsibilityPic(tmsLackGoodsApproved.getResponsibilityPic());

        return lackApprovedEntity;
    }

    public static TmsLackGoodsApproved entity2do(LackApprovedEntity lackApprovedEntity) {
        if(lackApprovedEntity == null){
            return null;
        }
        TmsLackGoodsApproved lackGoodsApproved = new TmsLackGoodsApproved();

        lackGoodsApproved.setId(lackApprovedEntity.getId());
        lackGoodsApproved.setStoreNo(lackApprovedEntity.getStoreNo());
        lackGoodsApproved.setWarehouseNo(lackApprovedEntity.getWarehouseNo());
        lackGoodsApproved.setSku(lackApprovedEntity.getSku());
        lackGoodsApproved.setMId(lackApprovedEntity.getMId());
        lackGoodsApproved.setLackNum(lackApprovedEntity.getLackNum());
        lackGoodsApproved.setMoney(lackApprovedEntity.getMoney());
        lackGoodsApproved.setLackType(lackApprovedEntity.getLackType());
        lackGoodsApproved.setRemark(lackApprovedEntity.getRemark());
        lackGoodsApproved.setResponsible(lackApprovedEntity.getResponsible());
        lackGoodsApproved.setState(lackApprovedEntity.getState());
        lackGoodsApproved.setBuyOut(lackApprovedEntity.getBuyOut());
        lackGoodsApproved.setBuyOutMoney(lackApprovedEntity.getBuyOutMoney());
        lackGoodsApproved.setJudgmentOpinion(lackApprovedEntity.getJudgmentOpinion());
        lackGoodsApproved.setStockTaskId(lackApprovedEntity.getStockTaskId());
        lackGoodsApproved.setPic(lackApprovedEntity.getPic());
        lackGoodsApproved.setCreateTime(lackApprovedEntity.getCreateTime());
        lackGoodsApproved.setUpdateTime(lackApprovedEntity.getUpdateTime());
        lackGoodsApproved.setApprovedAdminId(lackApprovedEntity.getApprovedAdminId());
        lackGoodsApproved.setApprovedTime(lackApprovedEntity.getApprovedTime());
        lackGoodsApproved.setResponsibilityAdminId(lackApprovedEntity.getResponsibilityAdminId());
        lackGoodsApproved.setResponsibilityTime(lackApprovedEntity.getResponsibilityTime());
        lackGoodsApproved.setFinishTime(lackApprovedEntity.getFinishTime());
        lackGoodsApproved.setOrderNo(lackApprovedEntity.getOrderNo());
        lackGoodsApproved.setAmount(lackApprovedEntity.getAmount());
        lackGoodsApproved.setStockLackNum(lackApprovedEntity.getStockLackNum());
        lackGoodsApproved.setTmsDeliverySiteId(lackApprovedEntity.getTmsDeliverySiteId());
        lackGoodsApproved.setApprovedName(lackApprovedEntity.getApprovedName());
        lackGoodsApproved.setResponsibilityName(lackApprovedEntity.getResponsibilityName());
        lackGoodsApproved.setAppealFlag(lackApprovedEntity.getAppealFlag());
        lackGoodsApproved.setResponsibilityPic(lackApprovedEntity.getResponsibilityPic());

        return lackGoodsApproved;
    }
}
