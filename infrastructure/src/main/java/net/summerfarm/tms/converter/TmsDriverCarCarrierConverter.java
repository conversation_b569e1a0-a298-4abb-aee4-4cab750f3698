package net.summerfarm.tms.converter;

import net.summerfarm.tms.base.driver.entity.DriverEntity;
import net.summerfarm.tms.dao.TmsDriverCarCarrierMapping;

/**
 * Description: <br/>
 * date: 2022/7/15 18:29<br/>
 *
 * <AUTHOR> />
 */
public class TmsDriverCarCarrierConverter {

    public static TmsDriverCarCarrierMapping entity2CityDriverCarCarrier(DriverEntity driverEntity) {

        TmsDriverCarCarrierMapping tmsDriverCarCarrierMapping = new TmsDriverCarCarrierMapping();

        tmsDriverCarCarrierMapping.setId(driverEntity.getCityMappingId());
        tmsDriverCarCarrierMapping.setCarrierId(driverEntity.getCityCarrierId());
        tmsDriverCarCarrierMapping.setTmsCarId(driverEntity.getCityCarId());
        tmsDriverCarCarrierMapping.setTmsDriverId(driverEntity.getId());
        tmsDriverCarCarrierMapping.setBusinessType(driverEntity.getBusinessType());
        tmsDriverCarCarrierMapping.setWarehouseSiteId(driverEntity.getCityWarehouseSiteId());

        return tmsDriverCarCarrierMapping;
    }

    public static DriverEntity cityDriverCarCarrier2Entity(TmsDriverCarCarrierMapping tmsDriverCarCarrierMapping) {
        DriverEntity driverEntity = new DriverEntity();
        if (tmsDriverCarCarrierMapping == null) {
            return null;
        }

        driverEntity.setId(tmsDriverCarCarrierMapping.getTmsDriverId());
        driverEntity.setCityMappingId(tmsDriverCarCarrierMapping.getId());
        driverEntity.setCityCarrierId(tmsDriverCarCarrierMapping.getCarrierId());
        driverEntity.setCityCarId(tmsDriverCarCarrierMapping.getTmsCarId());
        driverEntity.setCityWarehouseSiteId(tmsDriverCarCarrierMapping.getWarehouseSiteId());

        return driverEntity;
    }
}
