package net.summerfarm.tms.converter;

import net.summerfarm.tms.base.car.entity.CarEntity;
import net.summerfarm.tms.dao.TmsCar;
import net.summerfarm.tms.enums.CarStorageEnum;
import net.summerfarm.tms.enums.CarTypeEnum;

/**
 * Description: <br/>
 * date: 2022/7/15 12:02<br/>
 *
 * <AUTHOR> />
 */
public class TmsCarConverter {

    public static TmsCar entity2TmsCar(CarEntity carEntity) {

        TmsCar tmsCar = new TmsCar();

        tmsCar.setId(carEntity.getId());
        tmsCar.setCarNumber(carEntity.getCarNumber());
        tmsCar.setType(carEntity.getType());
        tmsCar.setVolume(carEntity.getVolume());
        tmsCar.setWeight(carEntity.getWeight());
        tmsCar.setCarPhotos(carEntity.getCarPhotos());
        tmsCar.setDriverPhotos(carEntity.getDriverPhotos());
        tmsCar.setStatus(carEntity.getStatus());
        tmsCar.setAdminId(carEntity.getAdminId());
        tmsCar.setCreateTime(carEntity.getCreateTime());
        tmsCar.setUpdateTime(carEntity.getUpdateTime());
        tmsCar.setStorage(carEntity.getCarStorageEnum() != null ? carEntity.getCarStorageEnum().getCode() : null);
        tmsCar.setCommercialInsuranceAmount(carEntity.getCommercialInsuranceAmount());
        tmsCar.setCommercialInsuranceExpireTime(carEntity.getCommercialInsuranceExpireTime());
        tmsCar.setCommercialInsurancePolicy(carEntity.getCommercialInsurancePolicy());
        tmsCar.setTrafficInsuranceAmount(carEntity.getTrafficInsuranceAmount());
        tmsCar.setTrafficInsuranceExpireTime(carEntity.getTrafficInsuranceExpireTime());
        tmsCar.setTrafficInsurancePolicy(carEntity.getTrafficInsurancePolicy());
        tmsCar.setQuantity(carEntity.getQuantity());
        return tmsCar;
    }

    public static CarEntity tmsCar2Entity(TmsCar tmsCar) {
        if (tmsCar == null) {
            return null;
        }
        CarEntity carEntity = new CarEntity();

        carEntity.setId(tmsCar.getId());
        carEntity.setCarNumber(tmsCar.getCarNumber());
        carEntity.setType(tmsCar.getType());
        carEntity.setVolume(tmsCar.getVolume());
        carEntity.setWeight(tmsCar.getWeight());
        carEntity.setCarPhotos(tmsCar.getCarPhotos());
        carEntity.setDriverPhotos(tmsCar.getDriverPhotos());
        carEntity.setStatus(tmsCar.getStatus());
        carEntity.setAdminId(tmsCar.getAdminId());
        carEntity.setCreateTime(tmsCar.getCreateTime());
        carEntity.setUpdateTime(tmsCar.getUpdateTime());
        carEntity.setCarStorageEnum(CarStorageEnum.getCarStorageByCode(tmsCar.getStorage()));
        carEntity.setCarTypeEnum(CarTypeEnum.getCarTypeByCode(tmsCar.getType()));
        carEntity.setCommercialInsuranceExpireTime(tmsCar.getCommercialInsuranceExpireTime());
        carEntity.setCommercialInsurancePolicy(tmsCar.getCommercialInsurancePolicy());
        carEntity.setCommercialInsuranceAmount(tmsCar.getCommercialInsuranceAmount());
        carEntity.setTrafficInsuranceExpireTime(tmsCar.getTrafficInsuranceExpireTime());
        carEntity.setTrafficInsurancePolicy(tmsCar.getTrafficInsurancePolicy());
        carEntity.setTrafficInsuranceAmount(tmsCar.getTrafficInsuranceAmount());
        carEntity.setQuantity(tmsCar.getQuantity());

        return carEntity;
    }
}
