package net.summerfarm.tms.converter;

import com.alibaba.fastjson.JSON;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.tms.dao.TmsDeliveryBatch;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.enums.DeliveryBatchEnums;
import net.summerfarm.tms.enums.DeliveryBatchStatusEnum;
import net.summerfarm.tms.jsonobject.DeliveryBatchLoadRatioCalculateJson;

import java.util.Optional;

/**
 * Description: <br/>
 * date: 2022/9/15 15:02<br/>
 *
 * <AUTHOR> />
 */
public class TmsDeliveryBatchConverter {

    public static TmsDeliveryBatch entityToTmsDeliveryBatch(DeliveryBatchEntity deliveryBatchEntity) {
        TmsDeliveryBatch tmsDeliveryBatch = new TmsDeliveryBatch();

        tmsDeliveryBatch.setId(deliveryBatchEntity.getId());
        tmsDeliveryBatch.setType(deliveryBatchEntity.getType());
        tmsDeliveryBatch.setCarId(deliveryBatchEntity.getCarId());
        tmsDeliveryBatch.setDriverId(deliveryBatchEntity.getDriverId());
        tmsDeliveryBatch.setCarrierId(deliveryBatchEntity.getCarrierId());
        tmsDeliveryBatch.setDeliveryTime(deliveryBatchEntity.getDeliveryTime());
        tmsDeliveryBatch.setPlanBeginTime(deliveryBatchEntity.getBeginTime());
        tmsDeliveryBatch.setEstimateFare(deliveryBatchEntity.getEstimateFare());
        tmsDeliveryBatch.setCreateId(deliveryBatchEntity.getCreateId());
        tmsDeliveryBatch.setCloseReason(deliveryBatchEntity.getCloseReason());
        tmsDeliveryBatch.setCreateTime(deliveryBatchEntity.getCreateTime());
        tmsDeliveryBatch.setStatus(deliveryBatchEntity.getStatus() != null ? deliveryBatchEntity.getStatus().getCode() : null);
        tmsDeliveryBatch.setPathName(deliveryBatchEntity.getPathName());
        tmsDeliveryBatch.setPathCode(deliveryBatchEntity.getPathCode());
        tmsDeliveryBatch.setFinishDeliveryTime(deliveryBatchEntity.getFinishDeliveryTime());
        tmsDeliveryBatch.setPathId(deliveryBatchEntity.getPathId());

        tmsDeliveryBatch.setPlanTotalDistance(deliveryBatchEntity.getPlanTotalDistance());
        tmsDeliveryBatch.setRealTotalDistance(deliveryBatchEntity.getRealTotalDistance());
        tmsDeliveryBatch.setBePathTime(deliveryBatchEntity.getBePathTime());
        tmsDeliveryBatch.setPickUpTime(deliveryBatchEntity.getPickUpTime());
        tmsDeliveryBatch.setFinishDeliveryTime(deliveryBatchEntity.getFinishDeliveryTime());
        //开始点位
        tmsDeliveryBatch.setBeginSiteId(deliveryBatchEntity.getBeginSiteId());
        //结束点位
        tmsDeliveryBatch.setEndSiteId(deliveryBatchEntity.getEndSiteId());
        tmsDeliveryBatch.setIntelligenceTotalDistance(deliveryBatchEntity.getIntelligenceTotalDistance());

        tmsDeliveryBatch.setArea(deliveryBatchEntity.getArea());
        tmsDeliveryBatch.setClasses(deliveryBatchEntity.getClasses());
        tmsDeliveryBatch.setRemark(deliveryBatchEntity.getRemark());
        tmsDeliveryBatch.setCloseUser(deliveryBatchEntity.getCloseUser());
        tmsDeliveryBatch.setCarryType(deliveryBatchEntity.getCarryType() != null ? deliveryBatchEntity.getCarryType().getValue() : null);
        return tmsDeliveryBatch;
    }

    public static DeliveryBatchEntity tmsDeliveryBatchToEntity(TmsDeliveryBatch tmsDeliveryBatch) {
        DeliveryBatchEntity deliveryBatchEntity = new DeliveryBatchEntity();
        if (tmsDeliveryBatch == null) {
            return deliveryBatchEntity;
        }

        deliveryBatchEntity.setId(tmsDeliveryBatch.getId());
        deliveryBatchEntity.setType(tmsDeliveryBatch.getType());
        deliveryBatchEntity.setCarId(tmsDeliveryBatch.getCarId());
        deliveryBatchEntity.setDriverId(tmsDeliveryBatch.getDriverId());
        deliveryBatchEntity.setCarrierId(tmsDeliveryBatch.getCarrierId());
        deliveryBatchEntity.setDeliveryTime(tmsDeliveryBatch.getDeliveryTime());
        deliveryBatchEntity.setBeginTime(tmsDeliveryBatch.getPlanBeginTime());
        deliveryBatchEntity.setEstimateFare(tmsDeliveryBatch.getEstimateFare());
        deliveryBatchEntity.setCreateId(tmsDeliveryBatch.getCreateId());
        deliveryBatchEntity.setCloseReason(tmsDeliveryBatch.getCloseReason());
        deliveryBatchEntity.setCreateTime(tmsDeliveryBatch.getCreateTime());
        deliveryBatchEntity.setStatus(DeliveryBatchStatusEnum.getBatchStatusByCode(tmsDeliveryBatch.getStatus()));
        deliveryBatchEntity.setPathCode(tmsDeliveryBatch.getPathCode());
        deliveryBatchEntity.setPathName(tmsDeliveryBatch.getPathName());
        deliveryBatchEntity.setPathId(tmsDeliveryBatch.getPathId());
        deliveryBatchEntity.setFinishDeliveryTime(tmsDeliveryBatch.getFinishDeliveryTime());
        deliveryBatchEntity.setBeginSiteId(tmsDeliveryBatch.getBeginSiteId());
        deliveryBatchEntity.setEndSiteId(tmsDeliveryBatch.getEndSiteId());
        deliveryBatchEntity.setBeginTime(tmsDeliveryBatch.getPlanBeginTime());
        deliveryBatchEntity.setPlanTotalDistance(tmsDeliveryBatch.getPlanTotalDistance());
        deliveryBatchEntity.setRealTotalDistance(tmsDeliveryBatch.getRealTotalDistance());
        deliveryBatchEntity.setBePathTime(tmsDeliveryBatch.getBePathTime());
        deliveryBatchEntity.setPickUpTime(tmsDeliveryBatch.getPickUpTime());
        deliveryBatchEntity.setFinishDeliveryTime(tmsDeliveryBatch.getFinishDeliveryTime());
        deliveryBatchEntity.setIntelligenceTotalDistance(tmsDeliveryBatch.getIntelligenceTotalDistance());
        deliveryBatchEntity.setArea(tmsDeliveryBatch.getArea());
        deliveryBatchEntity.setClasses(tmsDeliveryBatch.getClasses());
        deliveryBatchEntity.setRemark(tmsDeliveryBatch.getRemark());
        deliveryBatchEntity.setCloseUser(tmsDeliveryBatch.getCloseUser());
        deliveryBatchEntity.setUpdateTime(tmsDeliveryBatch.getUpdateTime());
        deliveryBatchEntity.setWeightLoadRatio(tmsDeliveryBatch.getWeightLoadRatio());
        deliveryBatchEntity.setVolumeLoadRatio(tmsDeliveryBatch.getVolumeLoadRatio());
        deliveryBatchEntity.setQuantityLoadRatio(tmsDeliveryBatch.getQuantityLoadRatio());
        deliveryBatchEntity.setCarryType(DeliveryBatchEnums.CarryType.getTypeByVal(tmsDeliveryBatch.getCarryType()));
        if(StringUtils.isNotBlank(tmsDeliveryBatch.getLoadRatioCalculateJson())){
            DeliveryBatchLoadRatioCalculateJson json = JSON.parseObject(tmsDeliveryBatch.getLoadRatioCalculateJson(), DeliveryBatchLoadRatioCalculateJson.class);
            deliveryBatchEntity.setCarLoadVolume(json.getCarLoadVolume());
            deliveryBatchEntity.setCarLoadQuantity(json.getCarLoadQuantity());
            deliveryBatchEntity.setCarLoadWeight(json.getCarLoadWeight());
            deliveryBatchEntity.setTransportationLoadVolume(json.getTransportationLoadVolume());
            deliveryBatchEntity.setTransportationLoadQuantity(json.getTransportationLoadQuantity());
            deliveryBatchEntity.setTransportationLoadWeight(json.getTransportationLoadWeight());
        }
        return deliveryBatchEntity;
    }
}
