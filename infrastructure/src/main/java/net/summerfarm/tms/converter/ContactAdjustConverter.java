package net.summerfarm.tms.converter;

import net.summerfarm.tms.base.carrier.entity.CarrierEntity;
import net.summerfarm.tms.base.site.entity.ContactAdjustEntity;
import net.summerfarm.tms.base.site.param.ContactAdjustCommandParam;
import net.summerfarm.tms.dao.Carrier;
import net.summerfarm.tms.dao.ContactAdjust;

/**
 * Description: <br/>
 * date: 2023/1/5 11:30<br/>
 *
 * <AUTHOR> />
 */
public class ContactAdjustConverter {

    public static ContactAdjustEntity do2Entity(ContactAdjust contactAdjust) {
        ContactAdjustEntity contactAdjustEntity = new ContactAdjustEntity();

        if(contactAdjust == null){
            return contactAdjustEntity;
        }
        contactAdjustEntity.setId(contactAdjust.getId());
        contactAdjustEntity.setAddTime(contactAdjust.getAddTime());
        contactAdjustEntity.setUpdateTime(contactAdjust.getUpdateTime());
        contactAdjustEntity.setMId(contactAdjust.getMId());
        contactAdjustEntity.setContactId(contactAdjust.getContactId());
        contactAdjustEntity.setNewPoi(contactAdjust.getNewPoi());
        contactAdjustEntity.setStatus(contactAdjust.getStatus());
        contactAdjustEntity.setNewProvince(contactAdjust.getNewProvince());
        contactAdjustEntity.setNewCity(contactAdjust.getNewCity());
        contactAdjustEntity.setNewArea(contactAdjust.getNewArea());
        contactAdjustEntity.setNewAddress(contactAdjust.getNewAddress());
        contactAdjustEntity.setNewHouseNumber(contactAdjust.getNewHouseNumber());

        return contactAdjustEntity;
    }

    public static ContactAdjust param2Do(ContactAdjustCommandParam commandParam) {
        if(commandParam == null){
            return null;
        }
        ContactAdjust contactAdjust = new ContactAdjust();
        contactAdjust.setAddTime(commandParam.getAddTime());
        contactAdjust.setUpdateTime(commandParam.getUpdateTime());
        contactAdjust.setMId(commandParam.getMId());
        contactAdjust.setContactId(commandParam.getContactId());
        contactAdjust.setNewPoi(commandParam.getNewPoi());
        contactAdjust.setStatus(commandParam.getStatus());
        contactAdjust.setNewProvince(commandParam.getNewProvince());
        contactAdjust.setNewCity(commandParam.getNewCity());
        contactAdjust.setNewArea(commandParam.getNewArea());
        contactAdjust.setNewAddress(commandParam.getNewAddress());
        contactAdjust.setNewHouseNumber(commandParam.getNewHouseNumber());
        return contactAdjust;
    }
}
