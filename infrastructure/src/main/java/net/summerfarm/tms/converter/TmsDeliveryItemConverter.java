package net.summerfarm.tms.converter;

import net.summerfarm.tms.dao.TmsDeliveryItem;
import net.summerfarm.tms.delivery.entity.DeliveryItemEntity;

/**
 * <AUTHOR>
 */
public class TmsDeliveryItemConverter {
    public static TmsDeliveryItem entity2Do(DeliveryItemEntity source) {
        TmsDeliveryItem result = new TmsDeliveryItem();
        result.setId(source.getId());
        result.setCreateTime(source.getCreateTime());
        result.setUpdateTime(source.getUpdateTime());
        result.setDeliveryOrderId(source.getDeliveryOrderId());
        result.setDistOrderId(source.getDistOrderId());
        result.setOutOrderId(source.getOutOrderId());
        result.setOutItemId(source.getOutItemId());
        result.setPlanReceiptCount(source.getPlanReceiptCount());
        result.setRealReceiptCount(source.getRealReceiptCount());
        result.setShortCount(source.getShortCount());
        result.setInterceptCount(source.getInterceptCount());
        result.setRejectCount(source.getRejectCount());
        result.setRejectRemark(source.getRejectRemark());
        result.setScanCount(source.getScanCount());
        result.setNoscanCount(source.getNoscanCount());
        result.setNoscanReason(source.getNoscanReason());
        result.setNoscanPics(source.getNoscanPics());
        result.setOutItemName(source.getOutItemName());
        result.setPackType(source.getPackType());
        result.setTemperature(source.getTemperature());

        return result;
    }

    public static DeliveryItemEntity do2Entity(TmsDeliveryItem source) {
        DeliveryItemEntity result = new DeliveryItemEntity();
        result.setId(source.getId());
        result.setCreateTime(source.getCreateTime());
        result.setUpdateTime(source.getUpdateTime());
        result.setDeliveryOrderId(source.getDeliveryOrderId());
        result.setDistOrderId(source.getDistOrderId());
        result.setOutOrderId(source.getOutOrderId());
        result.setOutItemId(source.getOutItemId());
        result.setPlanReceiptCount(source.getPlanReceiptCount());
        result.setRealReceiptCount(source.getRealReceiptCount());
        result.setShortCount(source.getShortCount());
        result.setInterceptCount(source.getInterceptCount());
        result.setRejectCount(source.getRejectCount());
        result.setRejectRemark(source.getRejectRemark());
        result.setScanCount(source.getScanCount());
        result.setNoscanCount(source.getNoscanCount());
        result.setNoscanReason(source.getNoscanReason());
        result.setNoscanPics(source.getNoscanPics());
        result.setOutItemName(source.getOutItemName());
        result.setPackType(source.getPackType());
        result.setTemperature(source.getTemperature());

        return result;
    }
}
