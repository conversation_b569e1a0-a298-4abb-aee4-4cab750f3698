package net.summerfarm.tms.converter;

import net.summerfarm.tms.dao.TmsDeliveryPick;
import net.summerfarm.tms.delivery.entity.DeliveryPickEntity;
import net.summerfarm.tms.enums.DeliveryPickEnums;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class TmsDeliveryPickConverter {
    public static TmsDeliveryPick entity2Do(DeliveryPickEntity deliveryPickEntity) {
        TmsDeliveryPick tmsDeliveryPick = new TmsDeliveryPick();
        tmsDeliveryPick.setId(deliveryPickEntity.getId());
        tmsDeliveryPick.setDeliveryBatchId(deliveryPickEntity.getDeliveryBatchId());
        tmsDeliveryPick.setSiteId(deliveryPickEntity.getSiteId());
        tmsDeliveryPick.setDriverId(deliveryPickEntity.getDriverId());
        tmsDeliveryPick.setOutItemId(deliveryPickEntity.getOutItemId());
        tmsDeliveryPick.setCategoryType(deliveryPickEntity.getCategoryType());
        tmsDeliveryPick.setItemDesc(deliveryPickEntity.getItemDesc());
        tmsDeliveryPick.setParticle(deliveryPickEntity.getParticle());
        tmsDeliveryPick.setType(deliveryPickEntity.getType());
        tmsDeliveryPick.setQuantity(deliveryPickEntity.getQuantity());
        tmsDeliveryPick.setPickQuantity(deliveryPickEntity.getPickQuantity());
        tmsDeliveryPick.setShortQuantity(deliveryPickEntity.getShortQuantity());
        tmsDeliveryPick.setInterceptQuantity(deliveryPickEntity.getInterceptQuantity());
        tmsDeliveryPick.setUnit(deliveryPickEntity.getUnit());
        tmsDeliveryPick.setStatus(deliveryPickEntity.getStatus());
        tmsDeliveryPick.setFinishTime(deliveryPickEntity.getFinishTime());
        tmsDeliveryPick.setTemperature(deliveryPickEntity.getTemperature());
        tmsDeliveryPick.setWeight(deliveryPickEntity.getWeight());
        tmsDeliveryPick.setDeliverySiteId(deliveryPickEntity.getDeliverySiteId());
        tmsDeliveryPick.setSpecification(deliveryPickEntity.getSpecification());

        tmsDeliveryPick.setProcessFlag(deliveryPickEntity.getProcessFlag());
        tmsDeliveryPick.setProcessQuantity(deliveryPickEntity.getProcessQuantity());
        tmsDeliveryPick.setProcessWeight(deliveryPickEntity.getProcessWeight());
        tmsDeliveryPick.setProcessUnit(deliveryPickEntity.getProcessUnit());
        tmsDeliveryPick.setProcessConversionRatio(deliveryPickEntity.getProcessConversionRatio());
        tmsDeliveryPick.setProcessPickQuantity(deliveryPickEntity.getProcessPickQuantity());
        tmsDeliveryPick.setProcessShortQuantity(deliveryPickEntity.getProcessShortQuantity());
        tmsDeliveryPick.setPackType(deliveryPickEntity.getPackType());
        tmsDeliveryPick.setOuterClientName(deliveryPickEntity.getOuterClientName());
        return tmsDeliveryPick;
    }

    public static DeliveryPickEntity do2Entity(TmsDeliveryPick tmsDeliveryPick) {
        if(tmsDeliveryPick == null){
            return null;
        }
        DeliveryPickEntity deliveryPickEntity = new DeliveryPickEntity();
        deliveryPickEntity.setId(tmsDeliveryPick.getId());
        deliveryPickEntity.setCreateTime(tmsDeliveryPick.getCreateTime());
        deliveryPickEntity.setUpdateTime(tmsDeliveryPick.getUpdateTime());
        deliveryPickEntity.setDeliveryBatchId(tmsDeliveryPick.getDeliveryBatchId());
        deliveryPickEntity.setSiteId(tmsDeliveryPick.getSiteId());
        deliveryPickEntity.setDriverId(tmsDeliveryPick.getDriverId());
        deliveryPickEntity.setOutItemId(tmsDeliveryPick.getOutItemId());
        deliveryPickEntity.setItemDesc(tmsDeliveryPick.getItemDesc());
        deliveryPickEntity.setParticle(tmsDeliveryPick.getParticle());
        deliveryPickEntity.setType(tmsDeliveryPick.getType());
        deliveryPickEntity.setQuantity(tmsDeliveryPick.getQuantity());
        deliveryPickEntity.setPickQuantity(tmsDeliveryPick.getPickQuantity());
        deliveryPickEntity.setShortQuantity(tmsDeliveryPick.getShortQuantity());
        deliveryPickEntity.setInterceptQuantity(tmsDeliveryPick.getInterceptQuantity());
        deliveryPickEntity.setUnit(tmsDeliveryPick.getUnit());
        deliveryPickEntity.setStatus(tmsDeliveryPick.getStatus());
        deliveryPickEntity.setFinishTime(tmsDeliveryPick.getFinishTime());
        deliveryPickEntity.setTemperature(tmsDeliveryPick.getTemperature());
        deliveryPickEntity.setWeight(tmsDeliveryPick.getWeight());
        deliveryPickEntity.setDeliverySiteId(tmsDeliveryPick.getDeliverySiteId());
        deliveryPickEntity.setSpecification(tmsDeliveryPick.getSpecification());

        deliveryPickEntity.setProcessFlag(tmsDeliveryPick.getProcessFlag() == null ? DeliveryPickEnums.ProcessFlag.NO_PROCESS_SKU.getValue()
                : tmsDeliveryPick.getProcessFlag());
        deliveryPickEntity.setProcessQuantity(tmsDeliveryPick.getProcessQuantity() == null ? 0 : tmsDeliveryPick.getProcessQuantity());
        deliveryPickEntity.setProcessWeight(tmsDeliveryPick.getProcessWeight() == null ? new BigDecimal(0) : tmsDeliveryPick.getProcessWeight());
        deliveryPickEntity.setProcessUnit(tmsDeliveryPick.getProcessUnit());
        deliveryPickEntity.setProcessConversionRatio(tmsDeliveryPick.getProcessConversionRatio() == null ? new BigDecimal(0) : tmsDeliveryPick.getProcessConversionRatio());
        deliveryPickEntity.setProcessPickQuantity(tmsDeliveryPick.getProcessPickQuantity() == null ? 0 : tmsDeliveryPick.getProcessPickQuantity());
        deliveryPickEntity.setProcessShortQuantity(tmsDeliveryPick.getProcessShortQuantity() == null ? 0 : tmsDeliveryPick.getProcessShortQuantity());
        deliveryPickEntity.setCategoryType(tmsDeliveryPick.getCategoryType());
        deliveryPickEntity.setPackType(tmsDeliveryPick.getPackType());
        deliveryPickEntity.setOuterClientName(tmsDeliveryPick.getOuterClientName());
        deliveryPickEntity.setScanCount(tmsDeliveryPick.getScanCount());

        return deliveryPickEntity;
    }
}
