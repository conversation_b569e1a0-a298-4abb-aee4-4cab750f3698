package net.summerfarm.tms.application.inbound.provider;

import net.summerfarm.tms.TmsApplication;import net.summerfarm.tms.client.alert.req.SummerfarmDeliveryAlertTimeQueryReq;
import net.summerfarm.tms.client.dist.req.BatchChangeDistOrdersFulfillmentDeliveryWayReq;
import net.summerfarm.tms.client.dist.req.BatchInterceptDistOrdersToDelayedDeliveryReq;
import net.summerfarm.tms.client.dist.req.DistOrderInterceptReq;
import net.summerfarm.tms.client.dist.resp.BatchInterceptDistOrdersToDelayedDeliveryResp;
import net.summerfarm.tms.client.dist.resp.DistOrderInterceptResp;
import net.summerfarm.tms.client.dist.resp.DistOrdersToChangeFulfillmentWayResp;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.provider.dist.TmsDistOrderCommandProviderImpl;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * Description: <br/>
 * date: 2024/12/25 11:47<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TmsApplication.class)
public class TmsDistOrderCommandProvderImplTest {
    @Resource
    private TmsDistOrderCommandProviderImpl tmsDistOrderCommandProvider;

    @Test
    public void batchInterceptDistOrdersToDelayedDelivery() {
        BatchInterceptDistOrdersToDelayedDeliveryReq req = new BatchInterceptDistOrdersToDelayedDeliveryReq();
        List<DistOrderInterceptReq> distOrderInterceptReqList = new ArrayList<>();
        DistOrderInterceptReq distOrderInterceptReq = new DistOrderInterceptReq();
        distOrderInterceptReq.setDeliveryTime(LocalDate.now().plusDays(1));
        distOrderInterceptReq.setSource(DistOrderSourceEnum.XM_MALL);
        distOrderInterceptReq.setOuterContactId("341231");
        distOrderInterceptReq.setOutOrderId("0124R6118L1231160134");

        distOrderInterceptReqList.add(distOrderInterceptReq);
        req.setDistOrderInterceptReqList(distOrderInterceptReqList);
        DubboResponse<BatchInterceptDistOrdersToDelayedDeliveryResp> batchInterceptDistOrdersToDelayedDeliveryRespDubboResponse = tmsDistOrderCommandProvider.batchInterceptDistOrdersToDelayedDelivery(req);
        System.out.println(batchInterceptDistOrdersToDelayedDeliveryRespDubboResponse);
    }


    @Test
    public void interceptDistOrder() {
        List<DistOrderInterceptReq> list = new ArrayList<>();
        DistOrderInterceptReq req = new DistOrderInterceptReq();

        req.setDeliveryTime(LocalDate.now().plusDays(1));
        req.setSource(DistOrderSourceEnum.XM_MALL);
        req.setOuterContactId("350723");
        req.setOutOrderId("04255TCCKN0725171018");

        list.add(req);

        DubboResponse<List<DistOrderInterceptResp>> listDubboResponse = tmsDistOrderCommandProvider.interceptDistOrder(list);
        System.out.println(listDubboResponse);
    }

    @Test
    public void batchChangeDistOrdersFulfillmentDeliveryWay(){
        List<BatchChangeDistOrdersFulfillmentDeliveryWayReq.changeDistOrdersFulfillmentDeliveryWayReq> reqList =new ArrayList<>();
        BatchChangeDistOrdersFulfillmentDeliveryWayReq.changeDistOrdersFulfillmentDeliveryWayReq detailRep = new BatchChangeDistOrdersFulfillmentDeliveryWayReq.changeDistOrdersFulfillmentDeliveryWayReq();
        detailRep.setOutOrderId("AS1948333205697167360");
        detailRep.setSource(DistOrderSourceEnum.XM_AFTER_SALE.getCode());
        detailRep.setOuterContactId("2591");
        detailRep.setDeliveryTime(LocalDate.now());
        reqList.add(detailRep);

      /*  BatchChangeDistOrdersFulfillmentDeliveryWayReq.changeDistOrdersFulfillmentDeliveryWayReq detailRep2 = new BatchChangeDistOrdersFulfillmentDeliveryWayReq.changeDistOrdersFulfillmentDeliveryWayReq();
        detailRep2.setOutOrderId("0425VEJFMK0721154282");
        detailRep2.setSource(DistOrderSourceEnum.XM_MALL.getCode());
        detailRep2.setOuterContactId("350723");
        detailRep2.setDeliveryTime(LocalDate.now().plusDays(1));
        reqList.add(detailRep2);
*/
        BatchChangeDistOrdersFulfillmentDeliveryWayReq req = new BatchChangeDistOrdersFulfillmentDeliveryWayReq();
        req.setChangeToFulfillmentDeliveryWay(4);
        req.setReqList(reqList);
        DubboResponse<DistOrdersToChangeFulfillmentWayResp> dubboResponse = tmsDistOrderCommandProvider.batchChangeDistOrdersFulfillmentDeliveryWay(req);
        System.out.println(dubboResponse);
    }
}
