package net.summerfarm.tms.local.dist;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.client.dist.provider.TmsDistOrderQueryProvider;
import net.summerfarm.tms.client.dist.req.DeliverySiteOnlyCodeQueryReq;
import net.summerfarm.tms.client.dist.req.standard.DistOrderBatchQueryStandardReq;
import net.summerfarm.tms.client.dist.req.standard.DistOrderQueryStandardReq;
import net.summerfarm.tms.client.dist.resp.DeliverySiteOnlyCodeResp;
import net.summerfarm.tms.client.dist.resp.standard.DistOrderStandardResp;
import net.summerfarm.tms.cost.DeliveryCostService;
import net.summerfarm.tms.cost.dto.DeliveryCostDTO;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.dist.DistOrderDomainService;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.DistOrderService;
import net.summerfarm.tms.dist.dto.DistOrderCommand;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.enums.DeliveryCostEnums;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.provider.dist.TmsDistOrderQueryStandardProviderImpl;
import net.summerfarm.tms.query.delivery.DeliveryCostQuery;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.summerfarm.tms.schedulex.DeliveryBatchCreateProcessor;
import net.summerfarm.tms.util.DateUtils;
import net.summerfarm.tms.util.JsonUtil;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

/**
 * Description:配送成本测试类
 * date: 2023/2/24 14:25
 *
 * <AUTHOR>
 */

@RunWith(SpringRunner.class)
@SpringBootTest
public class DeliveryCostServiceImplTest {

    @Resource
    private DeliveryCostService deliveryCostService;
    @Resource
    private DistOrderService distOrderService;
    @Resource
    private DeliveryBatchService deliveryBatchService;
    @Resource
    private TmsDistOrderQueryProvider tmsDistOrderQueryProvider;
    @Resource
    private DeliveryBatchCreateProcessor deliveryBatchCreateProcessor;
    @Resource
    private TmsDistOrderQueryStandardProviderImpl tmsDistOrderQueryStandardProvider;
    @Resource
    private DistOrderDomainService distOrderDomainService;
    @Resource
    private DistOrderRepository distOrderRepository;

    @Test
    public void testCostCalculate() {
        deliveryCostService.costCalculate(3395L, null, DeliveryCostEnums.ChangeType.COST_CHANGE);
    }

    @Test
    public void testQueryCostList() {
        List<DeliveryCostDTO> deliveryCostDTOS = deliveryCostService.queryCostList(DeliveryCostQuery.builder().outBatchIds(Arrays.asList("23739", "23740", "23741", "23742", "23744", "23745")).build());
    }

    @Test
    public void testQueryDistOrderDetail(){
        DistOrderQuery distOrderQuery = DistOrderQuery.builder().outerOrderId("01168008293457946").source(DistOrderSourceEnum.XM_MALL.getCode())
                .expectBeginTime(LocalDate.parse("2023-03-29").atStartOfDay()).outerContactId("339548").build();
        TmsResult<DistOrderDTO> distOrderDTOTmsResult = distOrderService.queryDetail(distOrderQuery);
        System.out.println();
    }

    @Test
    public void testDiffBatchHandle() {
        deliveryBatchService.diffBatchHandle();
        System.out.println();
    }


    @Test
    public void testDeliveryBatchCreate() throws Exception {

        deliveryBatchCreateProcessor.process(null);
    }

    @Test
    public void testSubmitDistOrder() {
        DistOrderCommand distOrderCommand = JSON.parseObject("{\"beginSite\":{\"address\":\"上海市闵行区莲花南路3000号（普洛斯物流园1）\",\"area\":\"闵行区\",\"city\":\"上海市\",\"completeAddress\":\"上海市上海市闵行区上海市闵行区莲花南路3000号（普洛斯物流园1）\",\"name\":\"上海总仓\",\"outBusinessNo\":\"2\",\"poi\":\"121.430661,31.060132\",\"province\":\"上海市\",\"type\":2},\"creator\":\"汪鹏\",\"creatorId\":\"2011\",\"distOrderItemList\":[{\"outerItemId\":\"***********\",\"outerItemName\":\"测试1\",\"outerItemType\":\"778\",\"quantity\":2,\"specification\":\"0.25L*12盒/精品/临保\",\"temperature\":3,\"type\":0,\"unit\":\"箱\",\"volume\":1.000,\"weight\":1.00}],\"distOrderMark\":{\"expectBeginTime\":\"2023-07-08T00:00:00\",\"outerOrderId\":\"208781\",\"source\":\"DEMO_OUT\"},\"endSite\":{\"address\":\"浙江省杭州市西湖区龙章路6号\",\"area\":\"西湖区\",\"city\":\"杭州市\",\"completeAddress\":\"浙江省杭州市西湖区浙江省杭州市西湖区龙章路6号\",\"contactPerson\":\"456\",\"name\":\"杭州城配仓\",\"outBusinessNo\":\"1\",\"phone\":\"789\",\"poi\":\"120.058808,30.282479\",\"province\":\"浙江省\",\"type\":1},\"expectBeginTime\":\"2023-07-08T00:00:00\",\"outerOrderId\":\"208781\",\"pickType\":0,\"source\":103,\"type\":0}", DistOrderCommand.class);
        distOrderService.submitDistOrder(distOrderCommand);
    }

    @Test
    public void testDistOrderQuery() {
        DeliverySiteOnlyCodeQueryReq deliverySiteOnlyCodeQueryReq = new DeliverySiteOnlyCodeQueryReq();
        deliverySiteOnlyCodeQueryReq.setOrderNo("01166960073722333");
        deliverySiteOnlyCodeQueryReq.setSource(DistOrderSourceEnum.XM_MALL.getCode());
        deliverySiteOnlyCodeQueryReq.setContactId("337279");
        LocalDateTime localDateTime = DateUtils.date2LocalDateTime(DateUtils.string2Date("2022-11-28 00:00:00", DateUtils.LONG_DATE_FORMAT));
        deliverySiteOnlyCodeQueryReq.setDeliveryTime(localDateTime);
        DubboResponse<List<DeliverySiteOnlyCodeResp>> listDubboResponse = tmsDistOrderQueryProvider.queryDistOrderOnlyCode(deliverySiteOnlyCodeQueryReq);
        System.out.println("查询结果返回为" + JsonUtil.toJson(listDubboResponse));
    }

    @Test
    public void testQueryDistOrderDetailList() {
        DistOrderBatchQueryStandardReq req = new DistOrderBatchQueryStandardReq();
        DistOrderQueryStandardReq distOrderQueryStandardReq1 = new DistOrderQueryStandardReq();
        distOrderQueryStandardReq1.setOuterOrderId("01168257653219029");
        distOrderQueryStandardReq1.setOuterContactId("339757");
        distOrderQueryStandardReq1.setSource(200);
        distOrderQueryStandardReq1.setExpectBeginTime(LocalDateTime.of(2023, 4, 28,0,0,0));

        DistOrderQueryStandardReq distOrderQueryStandardReq2 = new DistOrderQueryStandardReq();
        distOrderQueryStandardReq2.setOuterOrderId("01168257660096104");
        distOrderQueryStandardReq2.setOuterContactId("339757");
        distOrderQueryStandardReq2.setSource(200);
        distOrderQueryStandardReq2.setExpectBeginTime(LocalDateTime.of(2023, 4, 28,0,0,0));


        req.setDistOrderQueryStandardReqs(Lists.newArrayList(distOrderQueryStandardReq1, distOrderQueryStandardReq2));
        DubboResponse<List<DistOrderStandardResp>> listDubboResponse = tmsDistOrderQueryStandardProvider.queryDistOrderDetailList(req);
        System.out.println("查询结果返回为" + JsonUtil.toJson(listDubboResponse));
    }

    @Test
    public void testQueryDistOrderDetail1() {
        DistOrderQueryStandardReq req = JSON.parseObject("{\"expectBeginTime\":\"2024-05-10T00:00:00\",\"outerContactId\":\"42167\",\"outerOrderId\":\"#XMTOON#20240509000005\",\"source\":220}", DistOrderQueryStandardReq.class);
        DubboResponse<DistOrderStandardResp> response = tmsDistOrderQueryStandardProvider.queryDistOrderDetail(req);
        System.out.println("查询结果返回为" + JsonUtil.toJson(response));
    }

    @Test
    public void testAutoMatchBatch(){
        DistOrderEntity distOrderEntity = distOrderRepository.queryDetail(9011607L);
        //委托单自动匹配
        List<Long> matchBatchIds = distOrderDomainService.autoMatchBatch(distOrderEntity);

        System.out.println(matchBatchIds);
    }
}
