spring:
  schedulerx2:
    appKey: Tx+138Ub2NRh2M2W3H62Jw==
    endpoint: acm.aliyun.com
    groupId: local_dev_mall_service_task
    namespace: 39ca7826-3009-4217-a25c-d15c6f1e7282

  # 数据库连接
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ***********************************************************************************************************************************************
    username: dev2
    password: xianmu619
    # tomcat
    tomcat:
      initial-size: 0
      max-active: 20
      max-idle: 20
      min-idle: 1
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000

  redis:
    port: 6379
    host: test-redis.summerfarm.net
    password: xianmu619
    timeout: 5000
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: 3000    # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 20      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
    database: 0
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 1    #dev1:0  dev2:1  dev3:2 dev4:4  qa:5
    jedis:
      pool:
        max-active: 100 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 1000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
#rocketmq
rocketmq:
  consumer:
    access-key: Rocketmq
    secret-key: Rocketmq
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: Rocketmq
    group: GID_tms
    secret-key: Rocketmq
    sendMsgTimeout: 10000

redis:
  port: 6379
  host: test-redis.summerfarm.net
  timeout: 5000
  password: xianmu619
  jedis:
    pool:
      max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
      max-wait: 3000    # 连接池最大阻塞等待时间（使用负值表示没有限制）
      max-idle: 20      # 连接池中的最大空闲连接
      min-idle: 5       # 连接池中的最小空闲连接
  database: 0

# 关闭es健康检查
management:
  health:
    elasticsearch:
      enabled: false

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    #    address: nacos://************:11000
    address: nacos://test-nacos.summerfarm.net:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: a9f94e14-0f25-4567-a038-b32e83829046
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
aspect:
  log:
    open: true


#manageAddress: http://manage-svc
manageAddress: https://devadmin.summerfarm.net
crmAddress: https://devadmin.summerfarm.net

saas:
  domain: http://cosfo-mall-svc
  host: http://cosfo-mall-svc

xm:
  log:
    enable: true
    resp: true
  oss:
    persistent-storage:
      bucketName: test-app-perm
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devossperm.summerfarm.net
    temporary-storage:
      bucketName: test-app-temp
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devosstemp.summerfarm.net

nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: 800ed4d6-a4fd-4345-86dd-8a4cc70c9bda