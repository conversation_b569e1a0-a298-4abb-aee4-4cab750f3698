spring:
  application:
    name: tms
  profiles:
    active: dev2
#    active: product

## Mybatis 配置
mybatis:
  type-aliases-package: net.summerfarm.tms.model.domain
  mapper-locations: classpath:net.summerfarm.tms.mapper/*.xml,classpath:net.summerfarm.tms.mapper.gray/*.xml,classpath:net/summerfarm/tms/mapper/outland/*.xml
# mybatis sql 打印
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

mybatis-plus:
  mapper-locations: classpath:net/summerfarm/tms/mapper/*.xml,classpath:net/summerfarm/tms/mapper/gray/*.xml,classpath:net/summerfarm/tms/mapper/outland/*.xml
  global-config:
    db-config:
      update-strategy: NOT_NULL
      field-strategy: not_empty
      id-type: auto
      db-type: mysql
  configuration:
    # sql 打印
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    call-setters-on-nulls: true
    map-underscore-to-camel-case: true
# 日志配置
logging:
  pattern:
    console: "%d - %msg%n"
  level:
    root:  INFO
    org.springframework:  INFO
    org.mybatis:  INFO
    net.summerfarm: INFO
server:
  port: 80
  servlet:
    session:
      timeout: 3600
    encoding:
      charset: UTF-8

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql
log-path:  ${APP_LOG_DIR:../log}
