spring:
  schedulerx2:
    endpoint: addr-hz-internal.edas.aliyun.com
    namespace: a8a6b0d2-d8ad-4c36-a384-2eb13d301331
    groupId: TMS
    appKey: hc+Xg8LixlEHzREfuGUAPw==

  # 数据库连接
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ****************************************************************************************************************
    username: xianmu
    password: Xm@sF7Be2GIn0
    # tomcat
    tomcat:
      initial-size: 0
      max-active: 20
      max-idle: 20
      min-idle: 1
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
  # jpa
  #  jpa:
  #    show-sql: true
  #    hibernate:
  #      ddl-auto: none
  # redis
  redis:
    port: 6379
#    host: ************
    host: r-bp1jbnfh3sc5wfrtrq.tairpena.rds.aliyuncs.com
    timeout: 6000
    password: summerfarm0619#
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: 3000    # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 20      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
    database: 0
#rocketmq
rocketmq:
  name-server: http://MQ_INST_1788664839736465_BXz3eVFS.cn-hangzhou.mq-internal.aliyuncs.com:8080
  producer:
    group: GID_tms
    enableMsgTrace: false
    access-key: LTAI5t6azTEqB3a2GyJ2KkCG
    secret-key: ******************************
  consumer:
    access-key: LTAI5t6azTEqB3a2GyJ2KkCG
    secret-key: ******************************
    enableMsgTrace: false
redis:
  port: 6379
  host: r-bp1jbnfh3sc5wfrtrq.tairpena.rds.aliyuncs.com
  timeout: 6000
  password: summerfarm0619#
  jedis:
    pool:
      max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
      max-wait: 3000    # 连接池最大阻塞等待时间（使用负值表示没有限制）
      max-idle: 20      # 连接池中的最大空闲连接
      min-idle: 5       # 连接池中的最小空闲连接
  database: 0

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://mse-c5bf59b0-nacos-ans.mse.aliyuncs.com:8848
    parameters:
      namespace: 93cffb5c-f43b-4456-8e2b-13312acd2c7c
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 5000
    retries: 0
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false

#manageAddress: http://manage-svc
manageAddress: https://admin.summerfarm.net

saas:
  domain: http://cosfo-mall-svc
  host: http://cosfo-mall-svc

xm:
  log:
    enable: true
    resp: true
  xm:
    oss:
      persistent-storage:
        bucketName: pro-app-perm
        endpoint: oss-cn-hangzhou.aliyuncs.com
        innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
        accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
        accessKeySecret: ******************************
        domain: ossperm.summerfarm.net
      temporary-storage:
        bucketName: pro-app-temp
        endpoint: oss-cn-hangzhou.aliyuncs.com
        innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
        accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
        accessKeySecret: ******************************
        domain: osstemp.summerfarm.net

nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: b781e552-933d-44c5-b642-49dd30c5ba5f