spring:
  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: 39ca7826-3009-4217-a25c-d15c6f1e7282
    groupId: TMS
    appKey: cajc984EYhj9cvrESUqnUw==

  # 数据库连接
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ******************************************************************************************
    username: test
    password: xianmu619
    # tomcat
    tomcat:
      initial-size: 0
      max-active: 20
      max-idle: 20
      min-idle: 1
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000

  redis:
    port: 6379
    host: test-redis.summerfarm.net
    password: xianmu619
    timeout: 5000
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: 3000    # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 20      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
    database: 0
#rocketmq
rocketmq:
  name-server: **************:9876
  producer:
    group: GID_tms

redis:
  port: 6379
  host: **************
  timeout: 5000
  password: xianmu619
  jedis:
    pool:
      max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
      max-wait: 3000    # 连接池最大阻塞等待时间（使用负值表示没有限制）
      max-idle: 20      # 连接池中的最大空闲连接
      min-idle: 5       # 连接池中的最小空闲连接
  database: 0

# 关闭es健康检查
management:
  health:
    elasticsearch:
      enabled: false

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://test-nacos.summerfarm.net:11000
    parameters:
      namespace: fac8164c-1da8-43d2-bf49-e187bda7fcb4
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 5000
    retries: 0
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
saas:
  domain: http://cosfo-mall-svc
  host: http://cosfo-mall-svc

xm:
  log:
    enable: true
    resp: true
  oss:
    persistent-storage:
      bucketName: test-app-perm
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devossperm.summerfarm.net
    temporary-storage:
      bucketName: test-app-temp
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devosstemp.summerfarm.net