<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
    PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
    "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
  <!-- 数据库驱动-->
<!--  <classPathEntry
      location="/Users/<USER>/Downloads/mysql-connector-java-8.0.20.jar"/>-->
  <context id="DB2Tables" targetRuntime="MyBatis3">

    <!--<property name="javaFileEncoding" value="UTF-8" />-->
    <!--<property name="suppressTypeWarnings" value="true" />-->

    <!--<plugin type="org.mybatis.generator.plugins.SerializablePlugin" />-->
    <!--<plugin type="org.mybatis.generator.plugins.CaseInsensitiveLikePlugin" />-->
    <!--<plugin type="org.mybatis.generator.plugins.RowBoundsPlugin" />-->
    <!--<plugin type="org.mybatis.generator.plugins.ToStringPlugin" />-->
    <!--<plugin type="org.mybatis.generator.plugins.CachePlugin" />-->

    <!--<plugin type="org.mybatis.generator.plugins.RenameExampleClassPlugin">-->
    <!--<property name="searchString" value="Mapper$" />-->
    <!--<property name="replaceString" value="Dao" />-->
    <!--</plugin>-->

    <commentGenerator>
      <property name="suppressDate" value="true"/>
      <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
      <property name="suppressAllComments" value="true"/>
    </commentGenerator>

    <!--<settings>-->
    <!--<setting name="mapUnderscoreToCamelCase" value="true"/>-->
    <!--</settings>-->

    <!--数据库链接URL，用户名、密码 -->
    <!--<jdbcConnection driverClass="com.mysql.jdbc.Driver" connectionURL="******************************************************************************************" userId="root" password="root@mysql">-->
    <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                    connectionURL="jdbc:mysql://*************:3306/xianmudb?useUnicode=true&amp;characterEncoding=utf-8"
                    userId="test" password="xianmu619">
      <!--<jdbcConnection driverClass="com.mysql.jdbc.Driver" connectionURL="********************************************************************************************" userId="root" password="SummerFarm0619">-->
    </jdbcConnection>
    <!-- 类型解析器 默认false，把JDBC DECIMAL 和 NUMERIC 类型解析为 Integer，为 true时把JDBC DECIMAL 和 NUMERIC 类型解析为java.math.BigDecimal -->
    <javaTypeResolver>
      <property name="forceBigDecimals" value="false"/>
      <!-- This property is used to specify whether MyBatis Generator should force the use of JSR-310 data types for DATE, TIME,
      and TIMESTAMP fields, rather than using java.util.Date -->
      <!--当useJSR310Types为true时，就会jdbc对应的日期类型会转成java8中的LocateDateTime类型，如果useJSR310Types为false，则还是转成java.util.Date类型-->
      <property name="useJSR310Types" value="true"/>
    </javaTypeResolver>
    <!-- 生成模型的包名和位置-->
    <javaModelGenerator targetPackage="net.summerfarm.tms.model.domain" targetProject="src/main/java">
      <property name="enableSubPackages" value="true"/>
      <property name="trimStrings" value="true"/>
    </javaModelGenerator>
    <!-- 生成映射文件的包名和位置-->
    <sqlMapGenerator targetPackage="net.summerfarm.tms.mapper" targetProject="src/main/resources">
      <property name="enableSubPackages" value="true"/>
    </sqlMapGenerator>
    <!-- 生成DAO的包名和位置-->
    <javaClientGenerator type="XMLMAPPER" targetPackage="net.summerfarm.tms.mapper" targetProject="src/main/java">
      <property name="enableSubPackages" value="true"/>
    </javaClientGenerator>
    <!-- 要生成的表 tableName是数据库中的表名或视图名 domainObjectName是实体类名-->
    <table tableName="tms_driver" domainObjectName="TmsDriver" enableCountByExample="false"
           enableUpdateByExample="false"
           enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>
  </context>
</generatorConfiguration>