spring:
  application:
    name: tms
  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: 0d617c6f-94db-41cb-96b9-c082be6baa0d
    groupId: TMS
    appKey: 6TFkBQ5vd0xkPYbS/5uYpQ==

  # 数据库连接
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ***********************************************************************************************************************************************
    username: test
    password: xianmu619
    # tomcat
    tomcat:
      initial-size: 0
      max-active: 20
      max-idle: 20
      min-idle: 1
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 5    #dev1:0  dev2:1  dev3:2 dev4:4  qa:5
    jedis:
      pool:
        max-active: 100 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 1000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  redis:
    port: 6379
    host: test-redis.summerfarm.net
    password: xianmu619
    timeout: 5000
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: 3000    # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 20      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
    database: 2
rocketmq:
  name-server: qa-mq-nameserver.summerfarm.net:9876
  producer:
    group: GID_tms

redis:
  port: 6379
  host: test-redis.summerfarm.net
  timeout: 5000
  password: xianmu619
  jedis:
    pool:
      max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
      max-wait: 3000    # 连接池最大阻塞等待时间（使用负值表示没有限制）
      max-idle: 20      # 连接池中的最大空闲连接
      min-idle: 5       # 连接池中的最小空闲连接
  database: 2

# 关闭es健康检查
management:
  health:
    elasticsearch:
      enabled: false

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://test-nacos.summerfarm.net:11000
    parameters:
      namespace: 34792f7a-aaa2-41ee-8a7f-53be483c2533
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 5000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false

manageAddress: http://manage-svc

## Mybatis 配置
mybatis:
  type-aliases-package: net.summerfarm.tms.model.domain
  mapper-locations: classpath:net.summerfarm.tms.mapper/*.xml,classpath:net.summerfarm.tms.mapper.gray/*.xml,classpath:net/summerfarm/tms/mapper/outland/*.xml
# mybatis sql 打印
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

mybatis-plus:
  mapper-locations: classpath:net/summerfarm/tms/mapper/*.xml,classpath:net/summerfarm/tms/mapper/gray/*.xml,classpath:net/summerfarm/tms/mapper/outland/*.xml
  global-config:
    db-config:
      update-strategy: NOT_NULL
      field-strategy: not_empty
      id-type: auto
      db-type: mysql
  configuration:
    # sql 打印
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    call-setters-on-nulls: true
    map-underscore-to-camel-case: true
# 日志配置
logging:
  pattern:
    console: "%d - %msg%n"
  level:
    root:  INFO
    org.springframework:  INFO
    org.mybatis:  INFO
    net.summerfarm: INFO
server:
  port: 80
  servlet:
    session:
      timeout: 3600
    encoding:
      charset: UTF-8

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql
log-path:  ${APP_LOG_DIR:../log}

saas:
  domain: http://cosfo-mall-svc
  host: http://cosfo-mall-svc

xm:
  log:
    enable: true
    resp: true
  oss:
    persistent-storage:
      bucketName: test-app-perm
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devossperm.summerfarm.net
    temporary-storage:
      bucketName: test-app-temp
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devosstemp.summerfarm.net

crmAddress: http://crm-svc