package net.summerfarm.tms.expense.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> xiang
 * create at:  2021-11-12
 */
@Data
public class ExpenseDetailVO extends ExpenseDetail {

    @ApiModelProperty(value = "接收图片")
    private List<String> pictures;

    @ApiModelProperty(value = "地址修改标识:0未修改，1修改")
    private Integer modify;

    @ApiModelProperty(value = "任务编号(配送信息编号)")
    private Integer deliveryPathId;

    @ApiModelProperty(value = "城配仓名称")
    private String storeName;

    @ApiModelProperty(value = "关联店铺")
    private String mName;

    /**
     * 配送方式 0-正常配送 1-专车配送
     */
    private Integer sendWay;

    @ApiModelProperty("配送时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate deliveryTime;
}
