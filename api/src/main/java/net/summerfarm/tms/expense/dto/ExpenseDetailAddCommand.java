package net.summerfarm.tms.expense.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/1/11 18:59<br/>
 *
 * <AUTHOR> />
 */
@Data
public class ExpenseDetailAddCommand extends BaseObject {

    private static final long serialVersionUID = -3184427472885006315L;

    private Integer id;
    /**
     * 配送信息id
     */
    private Integer deliveryPathId;
    /**
     * 运输单id
     */
    @NotNull(message = "deliverySiteId不能为空")
    private Long deliverySiteId;

    @NotEmpty(message = "expenseDetails不能为空")
    private List<ExpenseDetailCommand> expenseDetails;
}
