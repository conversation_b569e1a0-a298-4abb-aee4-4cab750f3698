package net.summerfarm.tms.expense.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 配送费用报销操作记录
 *
 * <AUTHOR>
 * @Date 2023-02-27
 **/
@Data
public class DeliveryExpenseRecordVO implements Serializable {
    private static final long serialVersionUID = 9219766311904768911L;

    /**
     * 申请时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applicationTime;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 审核状态
     */
    private Integer status;

    /**
     * 审核失败原因,当审核状态为拒绝的时候展示该字段
     */
    private String reason;

    /**
     * 审核人
     */
    private String auditName;


    /**
     * 审核时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;

}
