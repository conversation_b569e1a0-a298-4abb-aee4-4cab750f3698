package net.summerfarm.tms.expense.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ExpenseDetailCommand extends BaseObject {

    private static final long serialVersionUID = 6572708071229143441L;

    /**
     * 报销类型
     */
    private Integer type;

    /**
     * 报销起点
     */
    private String startAddress;

    /**
     * 报销终点
     */
    private String endAddress;

    /**
     * 报销里程
     */
    private BigDecimal mileage;

    /**
     * 报销金额
     */
    private BigDecimal amount;

    /**
     * 报销明细
     */
    private String remark;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 照片
     */
    private String photos;

    /**
     * 接收图片
     */
    private List<String> pictures;

    /**
     * 地址修改标识:0未修改 1修改
     */
    private Integer modify;
}