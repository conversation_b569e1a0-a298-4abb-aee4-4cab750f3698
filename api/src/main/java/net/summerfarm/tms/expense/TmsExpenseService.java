package net.summerfarm.tms.expense;

import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.delivery.dto.ExpenseDTO;

/**
 * Description: <br/>
 * date: 2022/10/21 18:39<br/>
 *
 * <AUTHOR> />
 */
public interface TmsExpenseService {
    /**
     * 费用审核
     *
     * @param expenseDTO 审核报文
     * @return 结果
     */
    TmsResult<Void> expenseAudit(ExpenseDTO expenseDTO);

    /**
     * 保存费用报销单
     *
     * @param expenseDTO
     * @return
     */
    TmsResult<Void> saveExpense(ExpenseDTO expenseDTO);
}
