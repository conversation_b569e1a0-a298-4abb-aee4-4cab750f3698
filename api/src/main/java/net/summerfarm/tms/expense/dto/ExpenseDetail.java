package net.summerfarm.tms.expense.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <AUTHOR> xiang
 * create at:  2021-11-11
 */
@Data
public class ExpenseDetail {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "报销单id")
    private Integer expenseId;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态")
    private Integer state;

    @ApiModelProperty(value = "报销类型")
    private Integer type;

    @ApiModelProperty(value = "照片")
    private String photos;

    @ApiModelProperty(value = "是否复核")
    private Integer isReview;

    @ApiModelProperty(value = "报销起点")
    private String startAddress;

    @ApiModelProperty(value = "报销终点")
    private String endAddress;

    @ApiModelProperty(value = "报销里程")
    private BigDecimal mileage;

    @ApiModelProperty(value = "报销金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "报销明细")
    private String remark;

    @ApiModelProperty(value = "修改人")
    private String updater;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

}
