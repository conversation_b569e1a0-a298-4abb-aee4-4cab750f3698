package net.summerfarm.tms.performance;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.performance.dto.*;
import net.summerfarm.tms.query.delivery.PerformanceReviewCityQuery;
import net.summerfarm.tms.query.delivery.PerformanceReviewDetailQuery;
import net.summerfarm.tms.query.delivery.PerformanceReviewTaskQuery;

import java.util.List;

/**
 * Description: 履约审核
 */
public interface DeliveryPerformanceReviewQueryService {
    /**
     * 查询城配履约出仓信息
     * @param performanceReviewQuery 查询
     * @return 结果
     */
    PageInfo<CityPerformanceVO> queryCitySignOutDeliveryPerformance(PerformanceReviewCityQuery performanceReviewQuery);

    /**
     * 查询城配履约签收信息
     * @param performanceReviewQuery 查询
     * @return 结果
     */
    PageInfo<CityPerformanceVO> queryCitySignInDeliveryPerformance(PerformanceReviewCityQuery performanceReviewQuery);

    /**
     * 分页查询履约任务信息
     * @param performanceReviewTaskQuery 查询
     */
    PageInfo<DeliveryPerformReviewTaskDTO> queryPerformanceReviewTask(PerformanceReviewTaskQuery performanceReviewTaskQuery);

    /**
     * 履约审核-审核任务-详情
     * @param performanceReviewTaskId 主键id
     * @return 结果
     */
    DeliveryPerformReviewTaskDTO queryPerformanceReviewTaskDetail(Long performanceReviewTaskId);

    /**
     * 查询城配详情
     * @param performanceReviewDetailQuery 查询
     * @return 结果
     */
    PageInfo<DeliveryPerformReviewDetailDTO> queryReviewDetailCity(PerformanceReviewDetailQuery performanceReviewDetailQuery);

    /**
     * 查询干线详情
     * @param performanceReviewDetailQuery 查询
     * @return 结果
     */
    PageInfo<ReviewDetailTrunkSignInVO> queryReviewDetailTrunkSignIn(PerformanceReviewDetailQuery performanceReviewDetailQuery);

    /**
     * 导出结果
     * @param performanceReviewTaskIds 任务ID
     * @return
     */
    String excelReviewTask(List<Long> performanceReviewTaskIds);

    List<String> signInPicKeyword();
}
