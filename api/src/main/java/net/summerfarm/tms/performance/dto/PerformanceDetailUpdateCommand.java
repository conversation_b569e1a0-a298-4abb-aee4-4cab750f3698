package net.summerfarm.tms.performance.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/7/4 15:33<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PerformanceDetailUpdateCommand {

    /**
     * 履约审核详情id
     */
    @NotNull(message = "performanceReviewDetailId不能为空")
    private Long performanceReviewDetailId;

    /**
     * 状态 0待审核 1无法审核 2合规 3不合规
     */
    @NotNull(message = "state不能为空")
    private Integer state;

    /**
     * 点位照片原因
     * 城配出仓 装载照片
     * 1000 保温措施不合格
     * 1001 无装车照片
     * 1002 无保温被
     * 1003 无冰板
     * 1004 无保温箱、干冰
     * 1005 冷藏数量不符
     * 1006 冷冻数量不符
     * 1007 干冰数量不符
     * 1008 冰板未完全复冻
     * 1009 蛋糕摆放不规范
     * 1010 冰板材料不合格
     * 1011 装车照片拍照不清晰
     * 1012 保温被未盖住货物
     * 1013 冰板数量不充足

     城配出仓 车牌照片
     1200 车牌不符
     1201 无车牌照

     * 城配出仓 冷冻照片
     * 1300 冷冻货物未按要求贴合干冰
     * 1301 蛋糕摆放不规范
     * 1302 冷冻数量不符
     * 1303 干冰数量不符
     * 1304 无保温箱、干冰
     * 1305 未上传冷冻货物保温照片
     *
     *
     * 城配出仓 冷藏照片
     * 1400 冷藏货物贴合冰板面不足
     * 1401 冰板未完全复冻
     * 1402 冰板材料不合格
     * 1403 装车照片拍照不清晰
     * 1404 冰板数量不充足
     * 1405 冷藏数量不符
     * 1406 无冰板
     * 1407 未上传冷藏货物保温照片
     *
     *
     城配-签收 门店抬头
     2000 门头不符
     2001 无门头照
     2002 门头不清晰

     城配-签收 门店抬头
     2201 签收单不合格
     2202 无签收单照片
     城配-签收 货物照片

     2401 未做保温措施
     2402 未测温

     干线-签收 到仓-签收照片
     3000 无车辆温度照片
     3001 其他

     干线-签收 出仓-装载照片
     3200 无整体货物照片

     干线-签收 出仓-封签照片
     3400 无封签照片
     */
    private List<String> sitePicReasons;

    /**
     * 判罚金额
     */
    private BigDecimal penaltyMoney;
}
