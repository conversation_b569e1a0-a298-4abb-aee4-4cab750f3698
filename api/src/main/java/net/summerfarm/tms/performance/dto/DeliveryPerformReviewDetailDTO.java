package net.summerfarm.tms.performance.dto;

import lombok.Data;
import net.summerfarm.tms.enums.DeliverySiteInterceptStateEnum;
import net.summerfarm.tms.enums.DeliverySiteStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/7/4 17:14<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryPerformReviewDetailDTO {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 履约审核任务ID
     */
    private Long performanceReviewTaskId;

    /**
     * 状态 0待审核 1无法审核 2合规 3不合规
     */
    private Integer state;
    /**
     * 装载照片
     */
    private String cityLoadPics;

    /**
     * 车牌照片
     */
    private String cityVehiclePlatePics;
    /**
     * 冷藏照片
     */
    private String refrigeratePics;

    /**
     * 冷冻照片
     */
    private String freezePics;
    /**
     * 门店抬头
     */
    private String cityDeliveryPic;

    /**
     * 签收面照片
     */
    private String citySignPic;

    /**
     * 货物照片
     */
    private String cityProductPic;

    /**
     * 签收照片
     */
    private String trunkSignPic;

    /**
     * 装载照片
     */
    private String trunkLoadPics;

    /**
     * 封签照片
     */
    private String trunkSealPics;
    /**
     * 配送/履约日期
     */
    private LocalDateTime deliveryTime;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 司机电话
     */
    private String driverPhone;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车型0:小面包车 1:中面包车 2:依维柯 3:小型货车 4: 4米2 5:6米8  6:7米6  7:7 9米6  8 :13米5 9:17米5
     */
    private Integer carType;

    /**
     * 车辆存储条件 0常温、1冷藏
     */
    private Integer carStorage;

    /**
     * 配送批次ID
     */
    private Long deliveryBatchId;

    /**
     * 配送点id
     */
    private Long deliverySiteId;

    /**
     * 点位id
     */
    private Long siteId;

    /**
     * 详情地址
     */
    private String siteAddress;

    /**
     * 外部客户名
     */
    private String outerClientName;

    /**
     * 外部品牌名
     */
    private String outerBrandName;

    /**
     * 开始点位Id
     */
    private Long beginSiteId;

    /**
     * 开始点位名称
     */
    private String beginSiteName;

    /**
     * 线路编码
     */
    private String pathCode;

    /**
     * 线路名称
     */
    private String pathName;

    /**
     * 批次类型 -1干线城配用车、0干线用车、1调拨用车、2采购用车、3大客户用车、4仓库用车、5城配用车
     */
    private Integer deliveryBatchType;

    /**
     * 配送方式 0正常配送 1专车配送
     */
    private Integer sendWay;

    /**
     * 是否超出距离   0 正常 1超出
     */
    private Integer outDistance;

    /**
     * 异常原因（签到备注）
     */
    private String signInRemark;

    /**
     * 是否正常签收，0：正常，1：不正常
     */
    private Integer signInStatus;

    /**
     * 判罚金额
     */
    private BigDecimal penaltyMoney;

    /**
     * 点位照片原因

     * 城配出仓 装载照片
     * 1000 保温措施不合格
     * 1001 无装车照片
     * 1002 无保温被
     * 1003 无冰板
     * 1004 无保温箱、干冰
     * 1005 冷藏数量不符
     * 1006 冷冻数量不符
     * 1007 干冰数量不符
     * 1008 冰板未完全复冻
     * 1009 蛋糕摆放不规范
     * 1010 冰板材料不合格
     * 1011 装车照片拍照不清晰
     * 1012 保温被未盖住货物
     * 1013 冰板数量不充足

     城配出仓 车牌照片
     1200 车牌不符
     1201 无车牌照

     城配-签收 门店抬头
     2000 门头不符
     2001 无门头照
     2002 门头不清晰

     城配-签收 门店抬头
     2201 签收单不合格
     2202 无签收单照片
     城配-签收 货物照片

     2401 未做保温措施
     2402 未测温

     干线-签收 到仓-签收照片
     3000 无车辆温度照片
     3001 其他

     干线-签收 出仓-装载照片
     3200 无整体货物照片

     干线-签收 出仓-封签照片
     3400 无封签照片
     */
    private String sitePicReason;

    /**
     * 温区条件
     */
    private String temperatureConditions;

    /**
     * 配送批次状态
     * @see  net.summerfarm.tms.enums.DeliveryBatchStatusEnum
     */
    private Integer batchStatus;

    /**
     * 配送点位状态
     * @see DeliverySiteStatusEnum
     */
    private Integer siteStatus;

    /**
     * 配送点位拦截状态
     * @see DeliverySiteInterceptStateEnum
     */
    private Integer siteInterceptState;

    /**
     * 出仓温度
     */
    private BigDecimal signOutTemperature;

    /**
     * 点位在路线上的顺序
     */
    private Integer sequence;

    /**
     * 申诉状态 :0待申诉 1申诉中 2申诉失败 3申诉成功 4已关闭
     */
    private Integer appealStatus;

    /**
     * 非水果冷藏数量
     */
    private Integer nonFruitColdNum;

    /**
     * 非水果冷冻数量
     */
    private Integer nonFruitFreezeNum;
}
