package net.summerfarm.tms.performance.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * Description: <br/>
 * date: 2023/7/4 15:33<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PerformanceTaskUpdateCommand {

    /**
     * 履约审核任务id
     */
    @NotNull(message = "performanceReviewTaskId不能为空")
    private Long performanceReviewTaskId;

    /**
     * 状态0审核中、1审核完成、2已关闭
     */
    @NotNull(message = "state不能为空")
    @Min(value = 0, message = "状态最小值为0")
    @Max(value = 2, message = "状态最大值为2")
    private Integer state;
}
