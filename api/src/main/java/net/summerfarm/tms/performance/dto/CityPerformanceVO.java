package net.summerfarm.tms.performance.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Description: <br/>
 * date: 2023/6/26 15:06<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CityPerformanceVO {

    /**
     * 配送路线
     */
    private String pathCode;

    /**
     * 路线名称
     */
    private String pathName;

    /**
     * 出仓温度
     */
    private BigDecimal signOutTemperature;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 司机号码
     */
    private String driverPhone;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车型
     */
    private String carType;

    /**
     * 存储条件
     */
    private String storageDesc;

    /**
     * 装载照片
     */
    private String loadPics;

    /**
     * 车牌照片
     */
    private String vehiclePlatePics;

    /**
     * 点位序号
     */
    private Integer sequence;

    /**
     * 配送方式 0正常配送 1专车配送
     */
    private Integer sendWay;

    /**
     * 地址
     */
    private String address;

    /**
     * 店铺名称
     */
    private String clientName;

    /**
     * 是否超出距离 0 正常 1超出
     */
    private Integer outDistance;

    /**
     * 配送存储条件
     */
    private String temperatureConditions;

    /**
     * 出仓状态  true已出仓 false未出仓
     * 不为空标识已出仓 signOutStatus
     */
    private Boolean outStoreFlag;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 门店抬头
     */
    private String deliveryPic;

    /**
     * 签收面照片
     */
    private String signPic;

    /**
     * 货物照片
     */
    private String productPic;

    /**
     * ture 已签收 false未签收
     */
    private Boolean isHaveSignFlag;

    /**
     * 冷藏照片
     */
    private String refrigeratePics;

    /**
     * 冷冻照片
     */
    private String freezePics;
}
