package net.summerfarm.tms.performance;

import net.summerfarm.tms.performance.dto.PerformanceDetailUpdateCommand;
import net.summerfarm.tms.performance.dto.PerformanceReviewTaskCommand;
import net.summerfarm.tms.performance.dto.PerformanceTaskUpdateCommand;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/6/29 17:15<br/>
 *
 * <AUTHOR> />
 */
public interface DeliveryPerformanceReviewCommandService {

    /**
     * 创建审核任务
     * @param performanceReviewTaskCommand 报文
     */
    void createPerformanceReviewTask(PerformanceReviewTaskCommand performanceReviewTaskCommand);

    /**
     * 状态变更
     * @param performanceTaskUpdateCommand 报文
     */
    void reviewTaskChangeState(PerformanceTaskUpdateCommand performanceTaskUpdateCommand);

    /**
     * 详情的审核
     * @param performanceDetailUpdateCommands 报文
     */
    void reviewDetailAudit(List<PerformanceDetailUpdateCommand> performanceDetailUpdateCommands);
}
