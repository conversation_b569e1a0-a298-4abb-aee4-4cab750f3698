package net.summerfarm.tms.performance.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/6/26 18:46<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PerformanceReviewTaskCommand  extends BaseObject {
    /**
     * 审核类型 0城配-出仓、1城配-签收、2干线-签收
     */
    private Integer reviewTaskType;

    /**
     * 名称
     */
    private String name;

    /**
     * 判罚标准
     */
    private BigDecimal penaltyStandards;

    /**
     * 城配仓名称
     */
    private List<String> storeNames;

    /**
     * 城配仓编号
     */
    private List<String> storeNos;

    /**
     * 开始时间
     */
    private LocalDate beginDeliveryTime;

    /**
     * 结束时间
     */
    private LocalDate endDeliveryTime;

    /**
     * 类型 0干线用车、1调拨用车、2采购用车、3大客户用车、4仓库用车
     */
    private List<Integer> deliveryBatchTypes;

    /**
     * 类型名称
     */
    private List<String> deliveryBatchTypeNames;

    /**
     * 审核模式
     * 0-人工审核，1-AI审核
     */
    private Integer reviewMode;
}
