package net.summerfarm.tms.performance.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/7/5 14:53<br/>
 *
 * <AUTHOR> />
 */
@Data
public class ReviewDetailTrunkSignInVO {

    /**
     * 履约审核详情id
     */
    private Long performanceReviewDetailId;
    /**
     * 配送日期
     */
    private LocalDate deliveryTime;

    /**
     * 路线名称
     */
    private String pathName;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 司机号码
     */
    private String driverPhone;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车型
     */
    private String carType;

    /**
     * 存储条件
     */
    private String storageDesc;

    /**
     * 状态 0待审核 1无法审核 2合规 3不合规
     */
    private Integer state;

    /**
     * 配送批次ID
     */
    private Long deliveryBatchId;

    /**
     * 干线类型 0干线用车、1调拨用车、2采购用车、3大客户用车、4仓库用车
     */
    private Integer deliveryBatchType;
    /**
     * 判罚金额
     */
    private BigDecimal penaltyMoney;

    /**
     * 审核干线信息
     */
    private List<ReviewTrunkSiteVO> reviewTrunkSiteVOList;

    /**
     * 申诉状态 :0待申诉 1申诉中 2申诉失败 3申诉成功 4已关闭
     */
    private Integer appealStatus;
}
