package net.summerfarm.tms.performance.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/7/4 13:47<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryPerformReviewTaskDTO {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 审核类型 0城配-出仓、1城配-签收、2干线-签收
     */
    private Integer reviewTaskType;

    /**
     * 名称
     */
    private String name;

    /**
     * 判罚标准
     */
    private BigDecimal penaltyStandards;

    /**
     * 城配仓名称
     */
    private List<String> storeNames;

    /**
     * 城配仓编号
     */
    private List<String> storeNos;
    /**
     * 调度类型名称
     */
    private List<String> batchTypeNames;
    /**
     * 调度类型集合
     */
    private List<Integer> batchTypes;

    /**
     * 开始时间
     */
    private LocalDate beginDeliveryTime;

    /**
     * 结束时间
     */
    private LocalDate endDeliveryTime;

    /**
     * 状态0审核中、1审核完成、2已关闭
     */
    private Integer state;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     *审核合规数
     */
    private long okNum;
    /**
     *审核不合规数
     */
    private long noOkNum;
    /**
     *待审核
     */
    private long waitAuditNum;
    /**
     *无法审核数
     */
    private long noAuditNum;

    /**
     * 起点点位信息
     */
    private List<PerformanceBeginSiteDTO> performanceBeginSiteDTOs;


    /**
     * 审核模式
     * 0-人工审核，1-AI审核
     */
    private Integer reviewMode;
}
