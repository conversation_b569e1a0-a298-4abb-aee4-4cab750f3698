package net.summerfarm.tms.performance.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ReviewTrunkSiteVO {

    /**
     * 履约审核详情id
     */
    private Long detailId;

    /**
     * 点位名称
     */
    private String siteName;

    /**
     * 签收照片
     */
    private String signInPics;

    /**
     * 装载照片
     */
    private String loadPics;

    /**
     * 封签照片
     */
    private String sealPics;

    /**
     * 距离下一个运输点位之间的距离
     */
    private BigDecimal nextSiteDistanceKm;

    /**
     *点位照片原因
     *
     * 城配出仓 装载照片
     * 1000 保温措施不合格
     * 1001 无装车照片
     * 1002 无保温被
     * 1003 无冰板
     * 1004 无保温箱、干冰
     * 1005 冷藏数量不符
     * 1006 冷冻数量不符
     * 1007 干冰数量不符
     * 1008 冰板未完全复冻
     * 1009 蛋糕摆放不规范
     * 1010 冰板材料不合格
     * 1011 装车照片拍照不清晰
     * 1012 保温被未盖住货物
     * 1013 冰板数量不充足
     *
     * 城配出仓 车牌照片
     * 1200 车牌不符
     * 1201 无车牌照
     *
     * 城配-签收 门店抬头
     * 2000 门头不符
     * 2001 无门头照
     * 2002 门头不清晰
     *
     * 城配-签收 门店抬头
     * 2201 签收单不合格
     * 2202 无签收单照片
     * 城配-签收 货物照片
     *
     * 2401 未做保温措施
     * 2402 未测温
     * 2403 无货物图片
     * 2404 货物图片不符
     *
     * 干线-签收 到仓-签收照片
     * 3000 无车辆温度照片
     * 3001 其他
     *
     * 干线-签收 出仓-装载照片
     * 3200 无整体货物照片
     *
     * 干线-签收 出仓-封签照片
     * 3400 无封签照片
     */
    private List<String> sitePicReasons;
}
