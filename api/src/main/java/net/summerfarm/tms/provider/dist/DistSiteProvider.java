package net.summerfarm.tms.provider.dist;

import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.dist.dto.DistOrderCommand;
import net.summerfarm.tms.dist.dto.DistOrderEndCommand;
import net.xianmu.common.result.DubboResponse;

/**
 * Description:委托单提供者
 * date: 2022/9/21 15:29
 *
 * <AUTHOR>
 */
public interface DistSiteProvider {

    /**
     * 根据id查询地址详情
     *
     * @param id 委托单命令
     * @return 远程调用结果
     */
    DubboResponse<SiteDTO> queryDistSiteById(Long id, Integer type);
}
