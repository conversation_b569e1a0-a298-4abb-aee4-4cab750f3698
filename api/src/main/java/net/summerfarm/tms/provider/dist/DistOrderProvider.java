package net.summerfarm.tms.provider.dist;

import net.summerfarm.tms.dist.dto.DistOrderCommand;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.dist.dto.DistOrderEndCommand;
import net.summerfarm.tms.dist.dto.DistOrderInterceptDTO;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.xianmu.common.result.DubboResponse;

import java.util.List;

/**
 * Description:委托单提供者
 * date: 2022/9/21 15:29
 *
 * <AUTHOR>
 */
public interface DistOrderProvider {

    /**
     * 提交委托单
     *
     * @param distOrderCommand 委托单命令
     * @return 远程调用结果
     */
    DubboResponse<Long> submitDistOrder(DistOrderCommand distOrderCommand);

    /**
     * 取消承运单
     *
     * @param distOrderEndCommand 委托单结束命令
     * @return 处理结果
     */
    DubboResponse<Void> cancelDistOrder(DistOrderEndCommand distOrderEndCommand);

    /**
     * 订单拦截
     *
     * @param distOrderInterceptCommands 订单拦截命令
     * @return 处理结果
     */
    @Deprecated
    DubboResponse<Void> interceptDistOrder(List<DistOrderInterceptDTO> distOrderInterceptDTOList);
}
