package net.summerfarm.tms.provider.delivery;

import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.delivery.dto.DeliverySectionDTO;
import net.summerfarm.tms.query.delivery.DeliverySectionQuery;
import net.xianmu.common.result.DubboResponse;

import java.time.LocalDate;
import java.util.List;

/**
 * Description: 批次查询提供者<br/>
 * date: 2022/12/2 14:29<br/>
 *
 * <AUTHOR> />
 */
public interface DeliveryBatchQueryProvider {

    /**
     * 获取批次信息
     * @param deliverySectionQuery 查询条件
     * @return
     */
    DubboResponse<List<DeliveryBatchDTO>> queryBatch(DeliverySectionQuery deliverySectionQuery);

    /**
     * 获取路段详情信息
     * @param deliverySectionQuery 查询条件
     * @return 路段详情信息
     */
    DubboResponse<List<DeliverySectionDTO>> queryDeliverySection(DeliverySectionQuery deliverySectionQuery);
}
