package net.summerfarm.tms.provider.dist;

import net.summerfarm.tms.dist.dto.DistBlackConfigDTO;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.dist.dto.DistOrderInterceptDTO;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.xianmu.common.result.DubboResponse;

import java.util.List;

/**
 * Description:委托单查询提供者
 * date: 2022/9/21 15:30
 *
 * <AUTHOR>
 */
public interface DistOrderQueryProvider {

    /**
     * 查询委托单详情
     *
     * @param distOrderQuery 委托单查询参数
     * @return 委托单详情
     */
    DubboResponse<DistOrderDTO> queryDetail(DistOrderQuery distOrderQuery);

    /**
     * 是否需要自动提交委托单
     *
     * @param distBlackConfigDTO 委托单命令
     * @return 远程调用结果
     */
    DubboResponse<Boolean> isAutoSubmitDistOrder(DistBlackConfigDTO distBlackConfigDTO);

    /**
     * 查询可以拦截的订单信息
     *
     * @param distOrderQuery 订单拦截信息
     * @return 委托单详情
     */
    DubboResponse<List<DistOrderInterceptDTO>> queryInterceptSiteDistOrder(DistOrderQuery distOrderQuery);
}
