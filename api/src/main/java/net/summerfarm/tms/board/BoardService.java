package net.summerfarm.tms.board;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.board.dto.BoardDTO;
import net.summerfarm.tms.enums.DeliveryCostEnums;
import net.summerfarm.tms.query.board.BoardQuery;

import javax.servlet.http.HttpServletResponse;

/**
 * Description: <br/>
 * date: 2023/5/17 15:28<br/>
 *
 * <AUTHOR> />
 */
public interface BoardService {

    /**
     * 查询数据看板信息
     * @param boardQuery 请求参数
     * @return 结果
     */
    PageInfo<BoardDTO> queryBoardData(BoardQuery boardQuery);

    /**
     * 下载数据看板
     * @param boardQuery 查询
     * @return 资源ID
     */
    Long downloadDataBoard(BoardQuery boardQuery);
}
