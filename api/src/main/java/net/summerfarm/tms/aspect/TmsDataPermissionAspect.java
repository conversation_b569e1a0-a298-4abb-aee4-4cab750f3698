package net.summerfarm.tms.aspect;

import com.alibaba.fastjson.JSON;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.anno.CityStoreNo;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.ext.AuthService;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.*;

/**
 * Description: <br/>
 * date: 2023/7/13 11:45<br/>
 *
 * <AUTHOR> />
 */
@Aspect
@Slf4j
@Component
public class TmsDataPermissionAspect {

    @Resource
    private AuthService authService;

    @Before("@annotation(net.summerfarm.tms.aspect.TmsDataPermission)")
    public void beforeXpi(JoinPoint joinPoint) throws Exception {
        try {
            UserBase user = UserInfoHolder.getUser();
            TmsAssert.notNull(user, ErrorCodeEnum.LOGIN_ERROR);
            //调用Auth查询当前登录人拥有的城配仓数据
            List<String> haveStoreList = authService.queryCityStoreUserPermission(user.getId());
            if(CollectionUtils.isEmpty(haveStoreList)){
                throw new TmsRuntimeException("无此城配仓数据权限");
            }
            //拥有全部的权限
            if(haveStoreList.size() == 1 && "-1".equals(haveStoreList.get(0))){
                return;
            }
            //获取注解上面的城配仓编号信息
            List<String> wantQueryStoreNoList = getAnnotationsStoreNoList(joinPoint);
            //如果参数的查询条件是空，判断是否需要默认当前用户的数据权限
            if(CollectionUtils.isEmpty(wantQueryStoreNoList)){
                isSetNeedPerStoreNos(haveStoreList,joinPoint);
            }

            log.info("用户想查询城配仓集合数据:{},当前拥有的数据权限城配仓集合:{}",wantQueryStoreNoList,haveStoreList);
            Optional<String> opt = wantQueryStoreNoList.stream().filter(storeNo -> !haveStoreList.contains(storeNo)).findAny();
            if(opt.isPresent()){
                throw new TmsRuntimeException("无此城配仓数据权限");
            }
        } catch (Exception e) {
            if(e instanceof TmsRuntimeException){
                throw e;
            }
            log.error("获取数据权限异常:{}",e.getMessage(),e);
        }
    }

    /**
     * 是否设置默认的数据查询
     * @param haveStoreList 当前用户拥有的城配仓数据权限
     * @param joinPoint 切点
     */
    private void isSetNeedPerStoreNos(List<String> haveStoreList, JoinPoint joinPoint) throws IllegalAccessException {
        Object[] args = joinPoint.getArgs();

        //参数注解上面
        Annotation[][] annotations = ((MethodSignature) joinPoint.getSignature()).getMethod().getParameterAnnotations();
        for (int i = 0; i < annotations.length; i++) {
            Object param = args[i];
            Annotation[] paramAnn = annotations[i];
            //参数为空，直接下一个参数
            if(param == null || paramAnn.length == 0){
                continue;
            }
            for (Annotation pAnnotation : paramAnn) {
                //这里判断当前注解是否为CityStoreNo
                if(pAnnotation instanceof CityStoreNo){
                    CityStoreNo cityStoreNo = (CityStoreNo)pAnnotation;
                    if(!cityStoreNo.needPerStoreNos()){
                        continue;
                    }
                    args[i] = haveStoreList;
                    log.info("数据权限设置查询默认值{}:{}",args[i],haveStoreList);
                }
            }}

        //参数实体里面
        for (Object arg : args) {
            Class<?> aClass = arg.getClass();
            Field[] declaredFields = aClass.getDeclaredFields();
            for (Field declaredField : declaredFields) {
                declaredField.setAccessible(true);
                if(!declaredField.isAnnotationPresent(CityStoreNo.class)){
                    continue;
                }
                CityStoreNo cityStoreNo = declaredField.getAnnotation(CityStoreNo.class);
                if(!cityStoreNo.needPerStoreNos()){
                    continue;
                }
                if("java.lang.String".equals(declaredField.getType().getName())){
                    declaredField.set(arg, String.join(",", haveStoreList));
                }else{
                    declaredField.set(arg,haveStoreList);
                }

                log.info("数据权限设置查询默认值{}:{}",declaredField.getName(),haveStoreList);
            }
        }
    }

    private List<String> getAnnotationsStoreNoList(JoinPoint joinPoint) throws IllegalAccessException {
        Object[] args = joinPoint.getArgs();

        //城配仓编号集合
        List<String> storeNoList = new ArrayList<>();
        //参数注解上面
        Annotation[][] annotations = ((MethodSignature) joinPoint.getSignature()).getMethod().getParameterAnnotations();
        for (int i = 0; i < annotations.length; i++) {
            Object param = args[i];
            Annotation[] paramAnn = annotations[i];
            //参数为空，直接下一个参数
            if(param == null || paramAnn.length == 0){
                continue;
            }
            for (Annotation pAnnotation : paramAnn) {
                //这里判断当前注解是否为CityStoreNo
                if(!(pAnnotation instanceof CityStoreNo)){
                    continue;
                }
                if(!(param instanceof Collection)){
                    //数据看板逗号分隔。。。坑
                    storeNoList.addAll(Arrays.asList(param.toString().split(",")));
                }else{
                    storeNoList.addAll(JSON.parseArray(JSON.toJSONString(param),String.class));
                }
            }}

        //参数实体里面
        for (Object arg : args) {
            Class<?> aClass = arg.getClass();
            Field[] declaredFields = aClass.getDeclaredFields();
            for (Field declaredField : declaredFields) {
                declaredField.setAccessible(true);
                if(!declaredField.isAnnotationPresent(CityStoreNo.class)){
                    continue;
                }
                if(declaredField.isAnnotationPresent(CityStoreNo.class)){
                    Object o1 = declaredField.get(arg);
                    if(o1 == null ){
                       continue;
                    }
                    if(!(o1 instanceof Collection)){
                        //数据看板逗号分隔。。。坑
                        if(StringUtil.isNotBlank(o1.toString())){
                            storeNoList.addAll(Arrays.asList(o1.toString().split(",")));
                        }
                    }else{
                        storeNoList.addAll(JSON.parseArray(JSON.toJSONString(o1),String.class));
                    }
                }
            }
        }
        return storeNoList;
    }
}
