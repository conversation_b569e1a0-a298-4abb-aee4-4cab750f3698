package net.summerfarm.tms.aspect;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.util.ThreadLocalUtil;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * Description: <br/>
 * date: 2022/10/20 14:12<br/>
 *
 * <AUTHOR> />
 */
@Aspect
@Slf4j
@Component
public class TokenAspect {

    @Before("@annotation(net.summerfarm.tms.aspect.Token)")
    public void beforeXpi() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        String token = request.getHeader("token");
        log.info("获取到token：{}",token);
        ThreadLocalUtil.getThreadLocal().put("token",token);
    }

    @After("@annotation(net.summerfarm.tms.aspect.Token)")
    public void afterXpi() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String token = request.getHeader("token");

        ThreadLocalUtil.removeValue("token");
        log.info("以摧毁token：{}",token);

    }

}
