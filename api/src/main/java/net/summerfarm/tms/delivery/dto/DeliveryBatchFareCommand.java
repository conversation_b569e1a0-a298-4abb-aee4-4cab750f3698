package net.summerfarm.tms.delivery.dto;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description:配送批次运费明细更新实体
 * date: 2023/5/25 16:53
 *
 * <AUTHOR>
 */
@Data
public class DeliveryBatchFareCommand implements Serializable {

    private static final long serialVersionUID = -3363071710681576898L;

    /**
     * 费用ID
     */
    private Long id;

    /**
     * 费用类型，10：喜茶费用，20：益禾堂费用，30：美团费用，40：大客户费用，50：调拨费用，60：采购费用
     */
    @NotNull(message = "费用类型不能为空")
    private Integer fareType;

    /**
     * 费用金额
     */
    @NotNull(message = "费用金额不能为空")
    @DecimalMin(value="0.01", message="费用金额不符合规范")
    @Digits(integer=10, fraction=2, message="费用金额不符合规范")
    private BigDecimal amount;
}
