package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * Description: <br/>
 * date: 2023/1/10 18:01<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryWithOutCodeCommand extends BaseObject {
    /**
     * 配送点位详情id
     */
    @NotNull(message = "deliverySiteItemId不能为空")
    private Long deliverySiteItemId;
    /**
     * 运输单id
     */
    @NotNull(message = "deliverySiteId不能为空")
    private Long deliverySiteId;

    /**
     * sku
     */
    @NotBlank(message = "sku不能为空")
    private String sku;

    /**
     * 数量
     */
    @NotNull(message = "quantity不能为空")
    private Integer quantity;

    /**
     * 原因备注
     */
    @NotNull(message = "原因备注(remake)不能为空")
    private String remake;

    /**
     * 图片链接
     */
    @NotNull(message = "pictures不能为空")
    private String pictures;

}
