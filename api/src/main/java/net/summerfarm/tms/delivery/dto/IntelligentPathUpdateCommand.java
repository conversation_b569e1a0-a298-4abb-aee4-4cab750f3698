package net.summerfarm.tms.delivery.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description: 智能排线<br/>
 * date: 2024/8/23 12:43<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntelligentPathUpdateCommand {
    /**
     * 批次id
     */
    private Long batchId;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 是否处理智能排线
     */
    private Boolean handleIntelligentFlag;

    /**
     * 是否完成排线 true 完成排线 false 未完成排线
     */
    private Boolean completePathFlag;
}
