package net.summerfarm.tms.delivery.input;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Description: 完成拣货操作<br/>
 * date: 2024/7/23 18:51<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PickUpFinishUpdateCommand {

    /**
     * 配送点位id
     */
    @NotNull(message = "deliverySiteId不能为空")
    private Long deliverySiteId;


    /**
     * 拣货详情
     */
    @Valid
    private List<PickUpFinishDetailInput> pickUpFinishDetailInputs;

}
