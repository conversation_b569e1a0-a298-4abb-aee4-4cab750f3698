package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * Description:配送完成点位回收命令
 * date: 2023/12/14 18:24
 *
 * <AUTHOR>
 */
@Data
public class DeliveryFinishSiteRecycleCommand extends BaseObject {

    private static final long serialVersionUID = 4189457493438030299L;

    /**
     * 配送点位物品ID
     */
    @NotNull(message = "配送点位物品ID不能为空")
    private Long deliverySiteItemId;

    /**
     * sku
     */
    @NotBlank(message = "sku不能为空")
    private String sku;

    /**
     * 回收照片
     */
    @NotBlank(message = "回收照片不能为空")
    private String recyclePics;

    /**
     * 回收状态，0：正常，1：异常
     */
    @NotNull(message = "回收状态不能为空")
    private Integer status;

    /**
     * 包装单位数量
     */
    @DecimalMax(value = "9999.99", message = "数量超出可填写限制")
    @DecimalMin(value = "0", message = "数量超出可填写限制")
    private BigDecimal specificationQuantity;

    /**
     * 包装单位
     */
    @NotBlank(message = "包装单位不能为空")
    private String specificationUnit;

    /**
     * 规格单位数量
     */
    @DecimalMax(value = "9999.99", message = "数量超出可填写限制")
    @DecimalMin(value = "0", message = "数量超出可填写限制")
    private BigDecimal basicSpecQuantity;

    /**
     * 规格单位
     */
    @NotBlank(message = "规格单位不能为空")
    private String basicSpecUnit;

    /**
     * 回收原因类型，1：回收数量不符，2：商品已损坏，3：店铺未开门下次回收，4：包装破损，5：客户已用不退了，6：其它
     */
    private Integer reasonType;

    /**
     * 异常说明备注
     */
    @Length(message = "超出100字的最长限制")
    private String remark;

}
