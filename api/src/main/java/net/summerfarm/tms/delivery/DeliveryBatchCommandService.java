package net.summerfarm.tms.delivery;

import net.summerfarm.tms.message.out.CalcTmsPathDistanceMessage;

import java.util.List;

/**
 * Description: 配送批次写操作服务<br/>
 * date: 2024/1/11 15:17<br/>
 *
 * <AUTHOR> />
 */
public interface DeliveryBatchCommandService {

    /**
     * 蚁群算法计算路径距离
     * @param msg 消息体
     */
    void antAlgorithmCalcDistance(CalcTmsPathDistanceMessage msg);

    /**
     * 批次蚁群算法计算路径距离
     * @param batchId 批次
     */
    void batchAntAlgorithmCalcDistance(Long batchId);

    /**
     * ortools算法计算路径距离
     * @param calcTmsPathDistanceMessage 消息体
     */
    void calcPathDistanceByOrTools(CalcTmsPathDistanceMessage calcTmsPathDistanceMessage);
}
