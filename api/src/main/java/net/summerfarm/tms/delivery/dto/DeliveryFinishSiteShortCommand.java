package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

/**
 * Description: <br/>
 * date: 2023/1/10 18:56<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryFinishSiteShortCommand extends BaseObject {

    private static final long serialVersionUID = 4347538288570776775L;
    /**
     * 配送点位详情id
     */
    private Long deliverySiteItemId;

    /**
     * sku
     */
    private String sku;

    /**
     * 缺货数量
     */
    private Integer shortCount;

    /**
     * 备注
     */
    private String remark;
}
