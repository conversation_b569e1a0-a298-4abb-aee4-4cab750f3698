package net.summerfarm.tms.delivery.dto.lackapproved;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/1/11 17:03<br/>
 *
 * <AUTHOR> />
 */
@Data
public class BatchLackToCondemnCommand extends BaseObject {
    private static final long serialVersionUID = 5422999211258570570L;

    @NotNull(message = "ids不能为空")
    private List<Long> ids;

    /**
     * 责任方 1仓库 2城配仓 3无法判责 4干线
     */
    @NotNull(message = "responsible不能为空")
    private Integer responsible;

    /**
     * 是否买赔 0是 1否
     */
    @NotNull(message = "buyOut不能为空")
    private Integer buyOut;

    /**
     * 买赔金额
     */
    private BigDecimal buyOutMoney;

    /**
     * 判责意见
     */
    private String judgmentOpinion;
}
