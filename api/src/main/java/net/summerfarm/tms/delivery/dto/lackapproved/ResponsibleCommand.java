package net.summerfarm.tms.delivery.dto.lackapproved;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class ResponsibleCommand {
    /**
     * 责任方 1仓库 2城配 3无法判责 4干线
     */
    @NotNull(message = "responsible不能为空")
    private Integer responsible;

    /**
     * 是否买赔 0是 1否
     */
    @NotNull(message = "buyOut不能为空")
    private Integer buyOut;

    /**
     * 买赔金额
     */
    private BigDecimal buyOutMoney;
}
