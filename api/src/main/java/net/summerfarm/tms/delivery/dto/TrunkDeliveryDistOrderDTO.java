package net.summerfarm.tms.delivery.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2022/9/19 19:41<br/>
 *
 * <AUTHOR> />
 */
@Data
public class TrunkDeliveryDistOrderDTO extends BaseObject {

    private static final long serialVersionUID = -1291546370206949443L;

    private Long id;

    /**
     * 委托单ID
     */
    private Long distId;
    /**
     * 关联单据号
     */
    private String outOrderId;
    /**
     * 委托单来源
     */
    private Integer source;
    /**
     * 委托单来源描述
     */
    private String sourceDesc;
    /**
     * 履约时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deliveryTime;

    /**
     * 起点Id
     */
    private Long beginSiteId;
    /**
     * 起点名称
     */
    private String beginSiteName;
    /**
     * 终点Id
     */
    private Long endSiteId;
    /**
     * 终点名称
     */
    private String endSiteName;
    /**
     * 冷冻重量
     */
    private BigDecimal freezeWeight;

    /**
     * 冷冻体积
     */
    private BigDecimal freezeVolume;

    /**
     * 冷冻件数
     */
    private Integer freezeQuantity;

    /**
     * 冷藏重量
     */
    private BigDecimal coldWeight;

    /**
     * 冷藏体积
     */
    private BigDecimal coldVolume;

    /**
     * 冷藏件数
     */
    private Integer coldQuantity;

    /**
     * 常温重量
     */
    private BigDecimal normalWeight;

    /**
     * 常温体积
     */
    private BigDecimal normalVolume;

    /**
     * 常温件数
     */
    private Integer normalQuantity;

    /**
     * 合计重量
     */
    private BigDecimal totalWeight;

    /**
     * 合计体积
     */
    private BigDecimal totalVolume;

    /**
     * 合计件数
     */
    private Integer totalQuantity;

    /**
     * 站点类型，0：无中转，1：起点中转，2：终点中转
     */
    private Integer siteType;

}
