package net.summerfarm.tms.delivery.dto.lackapproved;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Description: <br/>
 * date: 2023/9/8 16:34<br/>
 *
 * <AUTHOR> />
 */
@Data
public class LackResponsibleDTO {
    /**
     * primary key
     */
    private Long id;
    /**
     * 缺货核准主键ID
     */
    private Long approvedId;
    /**
     * 责任方 1仓库 2城配 3无法判责 4干线
     */
    private Integer responsible;
    /**
     * 是否买赔 0是 1否
     */
    private Integer buyOut;
    /**
     * 买赔金额
     */
    private BigDecimal buyOutMoney;
}
