package net.summerfarm.tms.delivery.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 配送点位物品扫码表
 */
@Data
public class DeliverySiteItemCodeDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 配送详情id
     */
    private Long deliverySiteItemId;

    /**
     * 运输单id
     */
    private Long deliverySiteId;

    /**
     * 外部条目id
     */
    private String outItemId;

    /**
     * 唯一码
     */
    private String onlyCode;


}
