package net.summerfarm.tms.delivery.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description:配送批次运费明细数据传输实体
 * date: 2023/5/25 16:53
 *
 * <AUTHOR>
 */
@Data
public class DeliveryBatchFareDTO implements Serializable {

    private static final long serialVersionUID = -3363071710681576898L;

    /**
     * 运费ID
     */
    private Long id;

    /**
     * 配送批次ID
     */
    private Long deliveryBatchId;

    /**
     * 费用类型，10：喜茶费用，20：益禾堂费用，30：美团费用，40：大客户费用，50：调拨费用，60：采购费用
     */
    private Integer fareType;

    /**
     * 费用描述
     */
    private String fareTypeDesc;

    /**
     * 费用金额
     */
    private BigDecimal amount;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
