package net.summerfarm.tms.delivery.dto.lackapproved;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/1/11 17:00<br/>
 *
 * <AUTHOR> />
 */
@Data
public class LackToCondemnCommand extends BaseObject {
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 责任方
     */
    @NotEmpty(message = "责任方不能为空")
    private List<ResponsibleCommand> responsibleCommandList;

    /**
     * 判责意见
     */
    private String judgmentOpinion;
    /**
     * 判责凭证
     */
    private String responsibilityPic;
}
