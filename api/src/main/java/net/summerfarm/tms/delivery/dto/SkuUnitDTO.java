package net.summerfarm.tms.delivery.dto;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Description:SKU单位数据传输对象
 * date: 2023/12/15 15:15
 *
 * <AUTHOR>
 */
@Data
public class SkuUnitDTO implements Serializable {

    private static final long serialVersionUID = 5476048590760642402L;

    /**
     * sku
     */
    private String sku;

    /**
     * 包装单位
     */
    private String specificationUnit;

    /**
     * 规格单位
     */
    private String basicSpecUnit;

    public void fallbackInit(String sku) {
        this.dataInit(sku, null, null);
    }

    public void dataInit(String sku, String specificationUnit, String basicSpecUnit) {
        this.sku = sku;
        if (StrUtil.isBlank(specificationUnit)){
            specificationUnit = "件";
        }
        if (StrUtil.isBlank(basicSpecUnit)){
            basicSpecUnit = "个";
        }
        this.specificationUnit = specificationUnit;
        this.basicSpecUnit = basicSpecUnit;
    }
}
