package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.enums.DeliveryBatchTypeEnum;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Description:配送批次关联关系数据传输实体
 * date: 2023/8/3 10:30
 *
 * <AUTHOR>
 */
@Data
public class DeliveryBatchRelationDTO implements Serializable {

    private static final long serialVersionUID = 2654103871042274117L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 真正关联配送批次ID
     */
    private Long deliveryBatchId;
    /**
     * 配送批次ID
     */
    private Long batchId;
    /**
     * 关联配送批次ID
     */
    private Long relateBatchId;
    /**
     * @see DeliveryBatchTypeEnum
     * 调度单类型
     * -1干线城配用车、0干线用车、1调拨用车、2采购用车、3大客户用车、4仓库用车、5城配用车
     */
    private Integer type;
    /**
     * 调度单类型描述
     */
    private String typeDesc;
    /**
     * 班次 0正常 1加班
     */
    private Integer classes;
    /**
     * 班次描述
     */
    private String classesDesc;
    /**
     * 线路名称
     */
    private String pathName;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 是否支持删除标识，0：否，1：是
     */
    private Integer deleteFlag;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车型描述
     */
    private String carType;

    /**
     * 车辆存储条件
     */
    private String storageName;

    public void setCreator(String creator) {
        this.creator = creator;
        this.deleteFlag = Objects.equals(Constants.SYSTEM, creator) ? 0 : 1;
    }
}
