package net.summerfarm.tms.delivery.dto.pick;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Description: 拣货扫码<br/>
 * date: 2024/8/7 16:52<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PickScanCommand {

    /**
     * 批次ID
     */
    @NotNull(message = "批次ID不能为空")
    private Long batchId;

    /**
     * 唯一溯源码
     */
    @NotBlank(message = "唯一溯源码不能为空")
    private String onlyCode;
}
