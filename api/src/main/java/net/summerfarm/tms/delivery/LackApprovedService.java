package net.summerfarm.tms.delivery;


import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.delivery.dto.lackapproved.AfterSaleInfoVO;
import net.summerfarm.tms.delivery.dto.lackapproved.AppealSaveCommand;
import net.summerfarm.tms.delivery.dto.lackapproved.LackGoodsApprovedDTO;
import net.summerfarm.tms.query.delivery.LackApprovedQuery;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 查询缺货核准service
 *
 * <AUTHOR>
 * @Date 2023-03-14
 **/
public interface LackApprovedService {

    /**
     * 查询缺货核准
     *
     * @param queryLackApprovedDTO 查询缺货核准请求参数
     * @return
     */
    PageInfo<LackGoodsApprovedDTO> queryPage(LackApprovedQuery queryLackApprovedDTO);


    /**
     * 查看缺货核准详情信息
     *
     * @param lackApprovedId 缺货核准id
     * @return
     */
    LackGoodsApprovedDTO getDetail(Long lackApprovedId);

    /**
     * 导出
     *
     * @param lackGoodsApprovedDTO
     * @param response
     * @throws IOException
     */
    void exportExcel(LackApprovedQuery lackGoodsApprovedDTO, HttpServletResponse response) throws IOException;

    /**
     * 根据ID获取缺货核准信息
     * @param id 主键
     * @return 结果
     */
    LackGoodsApprovedDTO queryById(Long id);

    /**
     * 缺货核准
     *
     * @param LackGoodsDTO 更新实体类
     * @return 无返回
     */
    TmsResult<Void> lackApproved(LackGoodsApprovedDTO LackGoodsDTO);

    /**
     * 缺货判责
     *
     * @param LackGoodsDTO 更新实体类
     * @return 无返回
     */
    TmsResult<Void> lackToCondemn(LackGoodsApprovedDTO LackGoodsDTO);

    /**
     * 批量判责
     *
     * @param LackGoodsDTO 更新实体类
     * @return 无返回
     */
    TmsResult<Void> batchLackToCondemn(LackGoodsApprovedDTO LackGoodsDTO);

    /**
     * 提交申诉信息
     * @param appealSaveCommand 申诉信息
     */
    void appealSave(AppealSaveCommand appealSaveCommand);

    /**
     * 查询售后补发单信息
     * @param id 缺货核准ID
     * @return 结果
     */
    List<AfterSaleInfoVO> queryAfterSaleInfo(Long id);

    /**
     * 发送缺货核准飞书信息
     */
    void sendLackMessageJob();

    /**
     * 历史数据初始化
     */
    void init();

    /**
     * 下载缺货核准记录
     * @param lackApprovedQuery 查询
     * @return 资源ID
     */
    Long downloadApprovedRecord(LackApprovedQuery lackApprovedQuery);
}
