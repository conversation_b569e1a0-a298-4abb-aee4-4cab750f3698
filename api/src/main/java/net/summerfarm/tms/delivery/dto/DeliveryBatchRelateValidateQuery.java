package net.summerfarm.tms.delivery.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description:配送批次关联校验查询
 * date: 2023/8/4 16:33
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryBatchRelateValidateQuery implements Serializable {

    private static final long serialVersionUID = 358107787982984657L;

    /**
     * 批次ID
     */
    @NotNull(message = "批次ID不能为空")
    private Long batchId;

    /**
     * 关联批次ID
     */
    @NotNull(message = "关联批次ID不能为空")
    private Long relateBatchId;
}
