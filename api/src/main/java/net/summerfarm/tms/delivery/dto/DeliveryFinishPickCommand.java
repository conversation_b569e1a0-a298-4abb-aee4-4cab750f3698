package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/1/10 17:14<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryFinishPickCommand extends BaseObject {
    /**
     * 配送点位id
     */
    @NotNull(message = "deliverySiteId不能为空")
    private Long deliverySiteId;
    /**
     * 拣货详情
     */
    private List<DeliveryPickDetailCommand> deliveryPickDetails;
}