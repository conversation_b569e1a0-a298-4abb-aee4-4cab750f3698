package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import javax.validation.constraints.NotNull;

/**
 * Description: <br/>
 * date: 2023/1/31 15:47<br/>
 *
 * <AUTHOR> />
 */
@Data
public class SiteShortCommand  extends BaseObject {
    @NotNull(message = "deliverySiteId不能为空")
    private Long deliverySiteId;
    /**
     * 缺货数量
     */
    @NotNull(message = "shortCount不能为空")
    private Integer shortCount;
    /**
     * deliverySiteItemId
     */
    @NotNull(message = "deliverySiteItemId不能为空")
    private Long deliverySiteItemId;
}
