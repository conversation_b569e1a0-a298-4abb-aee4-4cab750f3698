package net.summerfarm.tms.delivery;

import net.summerfarm.tms.client.delivery.req.DeliveryOutOrderQueryReq;
import net.summerfarm.tms.delivery.dto.DeliveryOrderDTO;
import net.summerfarm.tms.query.delivery.DeliveryOrderQuery;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/5/16 17:12<br/>
 *
 * <AUTHOR> />
 */
public interface DeliveryOrderService {

    /**
     * 查询配送单信息
     * @param deliveryOrderQuery 查询
     * @return 结果
     */
    List<DeliveryOrderDTO> queryList(DeliveryOrderQuery deliveryOrderQuery);

    /**
     * 查询配送单相关信息
     *
     * <AUTHOR>
     * @date 2025/2/25 15:22
     */
    List<DeliveryOrderDTO> selectListByCondition(DeliveryOutOrderQueryReq queryReq);
}
