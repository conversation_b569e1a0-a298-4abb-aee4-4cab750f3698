package net.summerfarm.tms.delivery.input;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Description: 完成拣货详情<br/>
 * date: 2024/7/24 10:44<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PickUpFinishDetailInput {
    /**
     * 拣货ID
     */
    @NotNull(message = "deliveryPickId不能为空")
    private Long deliveryPickId;

    /**
     * sku
     */
    @NotNull(message = "sku不能为空")
    private String sku;

    /**
     *1正常/0缺损
     */
    @NotNull(message = "detailStatus不能为空")
    private Integer detailStatus;

    /**
     * 缺损数量
     */
    private Integer shortQuantity;
}
