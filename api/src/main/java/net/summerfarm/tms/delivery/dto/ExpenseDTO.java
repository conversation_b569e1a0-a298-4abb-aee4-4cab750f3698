package net.summerfarm.tms.delivery.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/10/21 18:50<br/>
 *
 * <AUTHOR> />
 */
@Data
public class ExpenseDTO {

    private Integer id;

    /**
     * 司机id
     */
    private Integer driverId;

    /**
     * 配送信息id
     */
    private Integer deliveryPathId;

    /**
     * 配送时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 报销类型
     */
    private Integer type;

    /**
     * 报销状态
     */
    private Integer state;

    /**
     * 城配仓
     */
    private Integer storeNo;

    /**
     * 是否复核
     */
    private Integer isReview;

    /**
     * 店铺id
     */
    private Long mId;

    /**
     * 店铺名称
     */
    private String mname;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 审核状态
     */
    private Integer status;

    /**
     * 审核失败原因
     */
    private String reason;

    /**
     * 运输单id
     */
    private Long tmsDeliverySiteId;
    /**
     * 点位ID
     */
    private Long siteId;

    /**
     * 当前审核用户名称
     */
    private String username;

    /**
     * 报销明细
     */
    private List<ExpenseDetailDTO> expenseDetailDTOList;

}
