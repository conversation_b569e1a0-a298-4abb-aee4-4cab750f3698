package net.summerfarm.tms.delivery.dto.lackapproved;

import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * Description: <br/>
 * date: 2022/5/30 10:32<br/>
 *
 * <AUTHOR> />
 */
@Data
public class LackGoodsApprovedDTO implements Serializable {
    //任务编号
    private Long id;
    /**
     * 缺货核准ID集合
     */
    private List<Long> ids;
    //城配仓
    private Integer storeNo;
    ///库存仓
    private Integer warehouseNo;

    private Integer deliveryPathId;
    //sku
    private String sku;

    private Integer mId;

    private Integer lackNum;

    private BigDecimal money;

    //缺货类型1.总仓-少发;2.总仓-库存不足;3.总仓-发错货;4.司机-误操作;5.司机-配送丢失;6.干线运输破损;7.其它
    private String lackType;

    /**
     * 缺货类型集合
     */
    private List<Integer> lackTypes;

    private String remark;

    //状态 1 待核准 2待判责 3 已完成
    private Integer state;

    private String judgmentOpinion;

    private Integer stockTaskId;

    private String pic;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Integer approvedAdminId;

    private LocalDateTime approvedTime;

    private Integer responsibilityAdminId;

    private LocalDateTime responsibilityTime;

    private LocalDateTime finishTime;

    private String orderNo;

    private Integer amount;

    private Integer stockLackNum;

    /**
     * 运输单id
     */
    private Long tmsDeliverySiteId;

    private String mname;

    private String pdName;

    private String driverName;

    private String driverPhone;

    private String address;

    private String pathName;

    private String deliveryPic;
    //wmsBuilderService.batchBuildWMSInfo(processDetailVOS);
    private String weight;

    private BigDecimal totalMoney;

    //状态 1 待核准 2待判责 3 已完成
    private String stateStr;

    private String buyOutStr;

    private String responsibleStr;

    //城配仓
    private String storeNoStr;
    ///库存仓
    private String areaNoStr;

    private String phone;


    /**
     * 签收面照片
     */
    private String signPic;

    /**
     * 货物照片
     */
    private String productPic;

    /**
     * 批次id
     */
    private Long deliveryBatchId;

    /**
     * 缺货核准操作人
     */
    private String approvedName;

    /**
     * 判责操作人
     */
    private String responsibilityName;

    /**
     * 是否申诉 0未申诉 1已申诉
     */
    private Integer appealFlag;

    /**
     * 判责凭证
     */
    private String responsibilityPic;

    /**
     * 申诉信息
     */
    private List<LackAppealDTO> lackAppealDTOList;

    /**
     * 缺货核准责任方
     */
    private List<LackResponsibleDTO> lackResponsibleDTOList;

    /**
     * 订单来源
     */
    private Integer orderSource;

    public String findStoreNo() {
        if (Objects.isNull(this.storeNo)) {
            return null;
        }
        return this.storeNo.toString();
    }

    public String findWarehouseNo() {
        if (Objects.isNull(this.warehouseNo)) {
            return null;
        }
        return this.warehouseNo.toString();
    }

    public List<String> queryAppealInfos(){
        if(CollectionUtils.isEmpty(this.lackAppealDTOList)){
            return Collections.emptyList();
        }
        return this.lackAppealDTOList.stream().map(LackAppealDTO::queryAppealInfo).collect(Collectors.toList());
    }

    public String queryResponsibleInfos(){
        if(CollectionUtils.isEmpty(this.getLackResponsibleDTOList())){
            return null;
        }
        return this.getLackResponsibleDTOList().stream().map(LackAppealDTO::queryResponsibleInfos).collect(Collectors.joining("、"));
    }
}
