package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.enums.DeliverySiteItemEnums;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Description:配送点位物品回收数据传输对象
 * date: 2023/12/14 19:04
 *
 * <AUTHOR>
 */
@Data
public class DeliverySiteItemRecycleDTO implements Serializable {

    private static final long serialVersionUID = -4390828649130826574L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 配送点位物品ID
     */
    private Long deliverySiteItemId;

    /**
     * 配送点位ID
     */
    private Long deliverySiteId;

    /**
     * 外部货品ID(sku)
     */
    private String outItemId;

    /**
     * 回收照片
     */
    private String recyclePics;



    /**
     * 包装单位数量
     */
    private BigDecimal specificationQuantity;

    /**
     * 包装单位
     */
    private String specificationUnit;

    /**
     * 规格单位数量
     */
    private BigDecimal basicSpecQuantity;

    /**
     * 规格单位
     */
    private String basicSpecUnit;

    /**
     * 回收原因类型，1：回收数量不符，2：商品已损坏，3：店铺未开门下次回收，4：包装破损，5：客户已用不退了，6：其它
     */
    private Integer reasonType;

    /**
     * 回收原因类型描述
     */
    private String reasonTypeDesc;

    /**
     * 异常说明备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public String buildNewRemark(Integer status){
        if (Objects.equals(status, DeliverySiteItemEnums.Status.NORMAL.getValue())){
            return null;
        }
        StringBuilder newRemark = new StringBuilder("实际回收数量：");
        newRemark.append(this.specificationQuantity);
        newRemark.append(this.specificationUnit);
        newRemark.append(Constants.Symbol.PLUS);
        newRemark.append(this.basicSpecQuantity);
        newRemark.append(this.basicSpecUnit);
        newRemark.append("，回收备注：");
        newRemark.append(this.reasonTypeDesc);
        newRemark.append("，");
        newRemark.append(this.remark);
        return newRemark.toString();
    }
}
