package net.summerfarm.tms.delivery.dto.pick;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Description: 拣货缺货<br/>
 * date: 2024/8/15 18:54<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PickShortCommand {
    /**
     * 拣货Id
     */
    @NotNull(message = "拣货Id不能为空")
    private Long deliveryPickId;

    /**
     * 缺货数量
     */
    @NotNull(message = "缺货数量不能为空")
    @Min(value = 0, message = "缺货数量不能小于0")
    private Integer shortCnt;
}
