package net.summerfarm.tms.delivery.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 配送点位物品扫码表
 */
@Data
public class DeliverySiteProcessItemDTO extends BaseObject {
    /**
     * 单位
     */
    private String unit;
    /**
     * 重量
     */
    private String weight;

    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 加工标识 0 加工 1未加工
     */
    private int processFlag;
}
