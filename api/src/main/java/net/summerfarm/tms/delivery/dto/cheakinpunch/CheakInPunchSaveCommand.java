package net.summerfarm.tms.delivery.dto.cheakinpunch;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * Description: 到店打卡
 * date: 2023/10/25 18:54<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CheakInPunchSaveCommand {

    /**
     * 配送点位id
     */
    @NotNull(message = "配送点位id不能为空")
    private Long deliverySiteId;

    /**
     * 打卡范围km
     */
    @NotNull(message = "打卡范围km不能为空")
    private BigDecimal punchRange;

    /**
     * 打卡详细地址
     */
    @NotBlank(message = "打卡详细地址不能为空")
    private String punchAddress;

    /**
     * 打卡POI
     */
    @NotBlank(message = "打卡POI不能为空")
    private String punchPoi;

    /**
     * 打卡到店距离km
     */
    @NotNull(message = "打卡到店距离km不能为空")
    private BigDecimal distanceToStore;

    /**
     * 超区原因
     */
    private String exceedReason;
}
