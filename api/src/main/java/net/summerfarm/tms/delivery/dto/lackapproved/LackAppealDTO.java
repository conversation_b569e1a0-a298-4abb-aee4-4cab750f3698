package net.summerfarm.tms.delivery.dto.lackapproved;

import jodd.util.StringUtil;
import lombok.Data;
import net.summerfarm.tms.enums.TmsLackApprovedAppealEnums;
import net.summerfarm.tms.enums.TmsLackApprovedResponsibleEnums;

import java.time.LocalDateTime;
import java.util.StringJoiner;

/**
 * Description: <br/>
 * date: 2023/9/7 18:11<br/>
 *
 * <AUTHOR> />
 */
@Data
public class LackAppealDTO {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 缺货核准id
     */
    private Long approvedId;

    /**
     * 申诉方:1城配、2干线、3仓库
     */
    private Integer side;

    /**
     * 申诉说明
     */
    private String description;

    /**
     * 申诉凭证
     */
    private String certificate;

    /**
     * 申诉人名称
     */
    private String appealPeopleName;

    /**
     * 获取申诉信息
     * @param lackAppealDTO 申诉对象
     * @return 申诉方+申诉说明
     */
    public static String queryAppealInfo(LackAppealDTO lackAppealDTO){
        if(lackAppealDTO == null){
            return null;
        }
        StringJoiner sj = new StringJoiner(":");
        String siteName = TmsLackApprovedAppealEnums.sideMap.get(lackAppealDTO.getSide());
        if(StringUtil.isNotBlank(siteName)){
            sj.add(siteName).add(lackAppealDTO.getDescription());
        }
        return sj.toString();
    }


    /**
     * 获取判责信息
     * @param lackResponsibleDTO 判责对象
     * @return 判责方
     */
    public static String queryResponsibleInfos(LackResponsibleDTO lackResponsibleDTO){
        if(lackResponsibleDTO == null){
            return null;
        }
        return TmsLackApprovedResponsibleEnums.responsibleMap.get(lackResponsibleDTO.getResponsible());
    }
}
