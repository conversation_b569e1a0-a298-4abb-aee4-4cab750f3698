package net.summerfarm.tms.delivery;

import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteItemCodeDTO;
import net.summerfarm.tms.delivery.dto.DeliverySiteItemDTO;
import net.summerfarm.tms.delivery.dto.*;
import net.summerfarm.tms.delivery.dto.cheakinpunch.CheakInPunchSaveCommand;
import net.summerfarm.tms.delivery.dto.pick.PickScanCommand;
import net.summerfarm.tms.delivery.dto.pick.PickShortCommand;
import net.summerfarm.tms.delivery.input.PickUpFinishUpdateCommand;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.summerfarm.tms.query.delivery.SkuUnitQuery;

import java.util.List;

/**
 * 批次配送过程中的接口
 */
public interface DeliverySiteService {
    /**
     * 打卡(到达)
     *
     * @param deliverySiteDTO 请求入参
     * @return 返回结果
     */
    TmsResult<Void> signIn(DeliverySiteDTO deliverySiteDTO);

  /**
   * 完成拣货前的校验
   *
   * @param deliverySiteId
   * @return
   */
  TmsResult<String> validateFinishPick(Long deliverySiteId);

    /**
     * 完成拣货
     *
     * @param deliverySiteDTO
     * @return
     */
    TmsResult<Void> finishPick(DeliverySiteDTO deliverySiteDTO);

    /**
     * 打卡(出发)
     *
     * @param deliverySiteDTO 请求入参
     * @return 返回结果
     */
    TmsResult<Void> signOut(DeliverySiteDTO deliverySiteDTO);

  /**
   * 完成配送
   *
   * @param deliverySiteDTO 请求入参
   * @param checkScanNum    是否需要校验扫码数量（喜茶完成配送不需要进行扫码操作）
   * @return 返回结果
   */
  TmsResult<Void> finishDelivery(DeliverySiteDTO deliverySiteDTO, Boolean checkScanNum);

    /**
     * 批量编辑点位路线
     *
     * @param deliverySiteDTOs 请求入参
     * @return 返回结果
     */
    TmsResult<Void> changeSiteBatch(List<DeliverySiteDTO> deliverySiteDTOs);

    /**
     * 点位配送 扫码
     *
     * @param deliverySiteItemCodeDTO
     * @return
     */
    TmsResult<DeliverySiteItemCodeDTO> scanCodeSave(DeliverySiteItemCodeDTO deliverySiteItemCodeDTO);

    /**
     * 无码商品数量保存
     *
     * @param deliverySiteItemDTO
     * @return
     */
    TmsResult<Void> noCodeSave(DeliverySiteItemDTO deliverySiteItemDTO);

    /**
     * 路线点位的移除
     *
     * @param deliverySiteId 配送单id
     * @return wu
     */
    TmsResult<Void> siteRemove(Long deliverySiteId);

    /**
     * 退单
     * @param deliverySiteId 配送单id
     */
    TmsResult<Void> chargeBack(Long deliverySiteId);

    /**
     * 查询
     * @param deliverySiteQuery 查询
     * @return 结果
     */
    TmsResult<DeliverySiteDTO> query(DeliverySiteQuery deliverySiteQuery);

    /**
     * 查询点位是否存在换货
     * @param deliverySiteId 点位id
     * @return
     */
    TmsResult<Boolean> isHaveExchange(Long deliverySiteId);

    /**
     * 查询外部点位
     * @param deliverySiteId 配送单id
     * @return 查询外部点位
     */
    TmsResult<String> queryOutContactId(Long deliverySiteId);

    /**
     * 查询司机的打卡信息
     * @param deliveryBatchId
     */
    TmsResult<DeliverySiteDTO> queryPunch(DeliveryBatchDTO deliveryBatchId);

    /**
     * 根据id查询配送商品详情
     * @param deliverySiteId 配送id
     * @return 结果
     */
    TmsResult<DeliverySiteDTO> queryDeliverySiteDetail(Long deliverySiteId);

    /**
     * 小程序根据id查询配送商品详情
     * @param deliverySiteId 配送id
     * @return 结果
     */
    TmsResult<DeliverySiteDTO> queryDeliverySiteDetailApp(Long deliverySiteId);

    /**
     * 查看打卡记录
     * @param deliverySiteId 配送点位ID
     * @return 打卡记录详情
     */
    TmsResult<DeliverySiteDTO> punchDetail(Long deliverySiteId);

    /**
     * 到仓打卡
     * @param deliverySitePunchCommand 配送点位打卡参数类
     * @return 结果
     */
    TmsResult<Void> toPunch(DeliverySitePunchCommand deliverySitePunchCommand);

    /**
     * 出仓打卡
     * @param deliverySitePunchCommand 配送点位打卡参数类
     * @return 结果
     */
    TmsResult<Void> outPunch(DeliverySitePunchCommand deliverySitePunchCommand);

    /**
     * 查询poi对应地址
     * @param poi 当前poi
     * @return 结果
     */
    TmsResult<String> poi2address(String poi);

    /**
     *完成拣货通知
     * @param deliveryPickId 拣货id
     */
    void finishPickNotifyEvents(Long deliveryPickId);

    /**
     * 查询点位配送信息
     * @param deliverySiteQuery 查询参数
     * @return 结果
     */
    DeliverySiteDTO finishDeliverySiteQuery(DeliverySiteQuery deliverySiteQuery);

    /**
     * 完成排线修改配送方式
     * @param deliverySiteDTO 点位信息
     * @return 无
     */
    TmsResult<Void> changeSpecialCarSendWay(DeliverySiteDTO deliverySiteDTO);

    /**
     * 获取是否已经点击开始配送
     * @param batchId 批次id
     * @return 结果
     */
    TmsResult<Boolean> isBeginDelivery(Long batchId);

    /**
     * 点位缺货数量
     * @param siteShortCommand 请求
     * @return 无
     */
    TmsResult<Void> siteShort(SiteShortCommand siteShortCommand);
    /**
     * 根据配送点位查询点位详情和点位信息
     * @param deliverySiteId 配送点位id
     */
    DeliverySiteDTO queryWithSiteBatchById(Long deliverySiteId);

    /**
     * 司机配送评价
     * @param siteId
     */
    List<TmsDriverAppraiseDTO> queryDriverAppraise(Long siteId);

  /**
   * 查询点位配送详情
   * @param batchId 批次ID
   * @param siteId 点位ID
   * @return 结果
   */
    DeliverySiteDTO queryDeliverySiteSendDetail(Long batchId,Long siteId);

  DeliverySiteDTO queryById(Long deliverySiteId);

  /**
   * 设置点位备注信息
   * @param deliveryBatchId 批次Id
   * @param endSiteId 点位Id
   * @param sendRemark 配送备注
   */
    void sendRemarkHandle(Long deliveryBatchId, Long endSiteId, String sendRemark);

  /**
   * 点位冷藏条件的生成
   * @param deliveryBatchIds 批次Id集合
   */
  void siteTemConditionsCreate(List<Long> deliveryBatchIds);

  /**
   * 查询点位配送信息
   * @param query 查询
   * @return 结果
   */
  DeliverySiteDTO querySiteWithSiteItem(DeliverySiteQuery query);

  /**
   * 到店打卡
   * @param cheakInPunchSaveCommand 打卡信息
   */
  void cheakinPunch(CheakInPunchSaveCommand cheakInPunchSaveCommand);

  /**
   * 根据二维码查询对应的订单信息
   * @param code 二维码编号
   * @return 订单号
   */
    String queryOrderNoByCode(String code);

  /**
   * 相同起点、终点、配送时间多个配送点位补偿处理
   */
  void sameDeliverySiteHandle();

  /**
   * 查询SKU货品单位信息
   * @param skuUnitQuery 查询
   * @return 结果
   */
  SkuUnitDTO querySkuUnit(SkuUnitQuery skuUnitQuery);

  /**
   * 配送点位物品状态数据初始化
   */
  void siteItemStatusDataInit();

  /**
   * 配送单拦截状态检查
   * @param deliveryBatchId 批次Id
   * @param endSiteId 点位Id
   */
  void interceptStateCheak(Long deliveryBatchId, Long endSiteId);
  /**
   * 存在配送点位无对应配送单处理
   */
  void deliverySiteWithNoOrderHandle();

  /**
   * 拣货扫码
   * @param command 报文
   */
    String pickScan(PickScanCommand command);

  /**
   * 拣货缺货
   * @param command 报文
   */
  void pickShort(PickShortCommand command);

  /**
   * 查询站点的拣货详情信息
   * @param deliverySiteId 站点ID
   * @return
   */
    List<DeliveryPickDTO> queryPickUpDetail(Long deliverySiteId);

  /**
   * 干线完成拣货
   * @param command 操作
   */
  void trunkPickUpFinish(PickUpFinishUpdateCommand command);
}
