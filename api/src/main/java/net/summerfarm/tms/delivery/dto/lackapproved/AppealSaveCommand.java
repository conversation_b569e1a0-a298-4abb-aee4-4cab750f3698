package net.summerfarm.tms.delivery.dto.lackapproved;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Description: <br/>
 * date: 2023/9/6 16:27<br/>
 *
 * <AUTHOR> />
 */
@Data
public class AppealSaveCommand {

    /**
     * 缺货核准id
     */
    @NotNull(message = "缺货核准id不能为空")
    private Long approvedId;

    /**
     * 申诉方:1城配、2干线、3仓库
     */
    @NotNull(message = "申诉方不能为空")
    private Integer side;

    /**
     * 申诉说明
     */
    @NotBlank(message = "申诉说明不能为空")
    private String description;

    /**
     * 申诉凭证
     */
    private String certificate;
}
