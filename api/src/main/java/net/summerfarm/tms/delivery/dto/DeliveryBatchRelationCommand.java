package net.summerfarm.tms.delivery.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Description:配送批次关联关系更新实体
 * date: 2023/8/3 10:36
 *
 * <AUTHOR>
 */
@Data
public class DeliveryBatchRelationCommand implements Serializable {

    private static final long serialVersionUID = 7186947131050476240L;

    /**
     * 配送批次ID
     */
    @NotNull(message = "batchId不能为空")
    private Long batchId;
    /**
     * 关联配送批次ID
     */
    @NotNull(message = "relateBatchId不能为空")
    private Long relateBatchId;
    /**
     * 创建人
     */
    private String creator;
}
