package net.summerfarm.tms.delivery.dto.lackapproved;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/9/6 17:41<br/>
 *
 * <AUTHOR> />
 */
@Data
public class AfterSaleInfoVO {

    /**
     * 售后单号
     */
    private String afterSaleOrderNo;
    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 0鲜沐 1Saas
     */
    private String source;

    /**
     * 店铺ID
     */
    private String mId;

    private String sku;

    private Integer suitId;

    /**
     * 订单号
     */
    private String orderNo;

    private Integer status;

    private String mname;
}
