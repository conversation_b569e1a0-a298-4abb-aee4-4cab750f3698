package net.summerfarm.tms.delivery.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/1/10 17:05<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryPunchCommand extends BaseObject {
    /**
     * 打卡时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime punchTime;
    /**
     * 打卡距离
     */
    @NotNull(message = "punchDistance不能为空")
    private BigDecimal punchDistance;
    /**
     * 司机打卡地址Poi
     */
    @NotBlank(message = "punchAddressPoi不能为空")
    private String punchAddressPoi;
    /**
     * 需要打卡的id
     */
    @NotNull(message = "deliverySiteId不能为空")
    private Long deliverySiteId;
}
