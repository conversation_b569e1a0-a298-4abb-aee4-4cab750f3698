package net.summerfarm.tms.alert.dto;

import lombok.Data;
import net.summerfarm.tms.anno.CityStoreNo;

import java.util.List;

/**
 * Description:完成配送提醒命令
 * date: 2023/3/29 15:59
 *
 * <AUTHOR>
 */
@Data
public class CompleteDeliveryQuery {

    /**
     * 配送完成时间
     */
    private String completeDeliveryTime;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String region;

    /**
     * 区域集合
     */
    private List<String> regions;

    /**
     * 物流中心编号（配送仓编号)
     */
    @CityStoreNo
    private Integer storeNo;

    /**
     * 城配仓编号集合
     */
    @CityStoreNo(needPerStoreNos = true)
    private List<String> storeNos;

}
