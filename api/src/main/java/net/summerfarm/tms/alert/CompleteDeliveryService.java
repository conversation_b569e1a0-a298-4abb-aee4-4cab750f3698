package net.summerfarm.tms.alert;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.alert.dto.CompleteDeliveryCommand;
import net.summerfarm.tms.alert.dto.CompleteDeliveryQuery;
import net.summerfarm.tms.alert.dto.CompleteDeliveryVO;
import net.summerfarm.tms.base.TmsResult;

/**
 * <AUTHOR> 2021/07/10
 * 完成配送提醒
 */
public interface CompleteDeliveryService {

    void allAlert(CompleteDeliveryCommand completeDeliveryCommand);

    PageInfo<CompleteDeliveryVO> queryAlertPage(Integer pageIndex, Integer pageSize, CompleteDeliveryQuery completeDeliveryQuery);

    void editAlert(CompleteDeliveryCommand completeDeliveryCommand);

    void delAlert(Integer id);

    void changeStatus(Integer id, Integer status);

    /**
     * 根据城配仓查询行政区域
     * @param storeNo
     * @param address
     * @return
     */
    TmsResult getCityByStoreNo(Integer id, Integer storeNo, String address, String city);

    /**
     * 查询省市区
     * @param name
     * @return
     */
    TmsResult getCity(String name,Integer storeNo);

}
