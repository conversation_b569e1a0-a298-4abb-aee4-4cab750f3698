package net.summerfarm.tms.alert;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.alert.dto.*;
import net.summerfarm.tms.query.alert.DeliveryAlertQuery;
import net.summerfarm.tms.query.alert.DeliveryAlertRuleQuery;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Description:完成配送提醒接口
 * date: 2023/3/21 14:48
 *
 * <AUTHOR>
 */
public interface DeliveryAlertService {

    /**
     * 分页查询配送提醒特殊规则组-城配仓维度
     * @param deliveryAlertRuleQuery 配送提醒特殊规则查询
     * @return 分页实体
     */
    PageInfo<DeliveryAlertRuleGroupDTO> queryAlertRuleGroupPage(DeliveryAlertRuleQuery deliveryAlertRuleQuery);

    /**
     * 查询配送提醒特殊规则组详情-城配仓维度
     * @param storeNo 配送仓编号
     * @return 配送提醒特殊规则详情
     */
    List<DeliveryAlertRuleDTO> queryAlertRuleGroupDetail(Integer storeNo);

    /**
     * 删除配送提醒特殊规则组-城配仓维度
     * @param storeNo 配送仓编号
     */
    void removeAlertRuleGroup(Integer storeNo);

    /**
     * 编辑配送提醒特殊规则组-城配仓维度
     * @param deliveryAlertRuleGroupCommand 配送提醒特殊规则命令
     */
    void editAlertRuleGroup(DeliveryAlertRuleGroupCommand deliveryAlertRuleGroupCommand);

    /**
     * 分页查询配送提醒特殊规则-规则维度
     * @param deliveryAlertQuery 配送提醒查询
     * @return 分页实体
     */
    PageInfo<DeliveryAlertDTO> queryAlertRulePage(DeliveryAlertQuery deliveryAlertQuery);

    /**
     * 新增配送提醒特殊规则-规则维度
     * @param deliveryAlertRuleCommand 配送提醒规则命令
     */
    void addAlertRule(DeliveryAlertRuleCommand deliveryAlertRuleCommand);

    /**
     * 编辑配送提醒特殊规则-规则维度
     * @param deliveryAlertRuleCommand 配送提醒命规则令
     */
    void editAlertRule(DeliveryAlertRuleCommand deliveryAlertRuleCommand);

    /**
     * 删除配送提醒特殊规则-规则维度
     * @param ruleId 规则ID
     */
    void delAlertRule(Long ruleId);

    /**
     * 发送配送提醒
     */
    void sendAlert();

    /**
     * Excel导出未完成配送数据
     * @param postDate 提醒日期
     * @param postHour 提醒小时
     * @param areaComboStr 区域组合串
     * @param response 响应
     */
    void excel(String postDate, String postHour, String areaComboStr, HttpServletResponse response);
}
