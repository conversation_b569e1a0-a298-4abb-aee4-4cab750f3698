package net.summerfarm.tms.alert.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Description:配送提醒特殊规则命令
 * date: 2023/3/21 15:59
 *
 * <AUTHOR>
 */
@Data
public class DeliveryAlertRuleGroupCommand extends BaseObject {

    private static final long serialVersionUID = -4658443706316534032L;

    /**
     * 配送仓编号
     */
    @NotNull(message = "配送仓编号不能为空")
    private Integer storeNo;

    /**
     * 配送提醒规则集合
     */
    @Valid
    @NotEmpty(message = "配送提醒规则集合不能为空")
    List<DeliveryAlertRuleDTO> deliveryAlertRuleDTOList;

}
