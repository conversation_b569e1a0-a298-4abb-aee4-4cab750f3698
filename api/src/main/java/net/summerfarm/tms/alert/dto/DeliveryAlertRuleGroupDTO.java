package net.summerfarm.tms.alert.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import java.util.List;

/**
 * Description:配送提醒规则组
 * date: 2023/3/21 15:50
 *
 * <AUTHOR>
 */
@Data
public class DeliveryAlertRuleGroupDTO extends BaseObject {

    private static final long serialVersionUID = -8816675264896322069L;

    /**
     * 配送仓编号
     */
    private Integer storeNo;

    /**
     * 配送仓名称
     */
    private String storeName;

    /**
     * 配送提醒规则集合
     */
    List<DeliveryAlertRuleDTO> deliveryAlertRuleDTOList;
}
