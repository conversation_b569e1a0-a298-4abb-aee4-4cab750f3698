package net.summerfarm.tms.alert.dto;

import lombok.Data;
import net.summerfarm.tms.alert.group.DeliveryAlertRuleValidationGroups;
import net.summerfarm.tms.alert.group.DeliveryAlertValidationGroups;
import net.summerfarm.tms.base.BaseObject;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Description:配送提醒命令
 * date: 2023/3/21 15:59
 *
 * <AUTHOR>
 */
@Data
public class DeliveryAlertCommand extends BaseObject {

    private static final long serialVersionUID = 3568027299417748192L;

    /**
     * 主键
     */
    @NotNull(message = "配送提醒ID不能为空", groups = {DeliveryAlertValidationGroups.EditGroup.class, DeliveryAlertValidationGroups.EnDisAbleGroup.class})
    private Integer id;

    /**
     * 配送仓编号
     */
    @NotNull(message = "配送仓编号不能为空", groups = {DeliveryAlertValidationGroups.AddGroup.class, DeliveryAlertValidationGroups.EditGroup.class})
    private Integer storeNo;

    /**
     * 区域编号集合
     */
    @NotEmpty(message = " 区域编号集合不能为空", groups = {DeliveryAlertValidationGroups.AddGroup.class, DeliveryAlertValidationGroups.EditGroup.class})
    private List<String> adCodes;

    /**
     * 配送完成时间
     */
    @NotBlank(message = "配送完成时间不能为空", groups = {DeliveryAlertValidationGroups.AddGroup.class, DeliveryAlertValidationGroups.EditGroup.class})
    private String completeDeliveryTime;

    /**
     * 状态 0 正常 1 暂停
     */
    @NotNull(message = "状态不能为空", groups = {DeliveryAlertValidationGroups.EnDisAbleGroup.class})
    private Integer status;
}
