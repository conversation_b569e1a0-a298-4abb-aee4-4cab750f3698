package net.summerfarm.tms.alert.dto;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * Description:配送提醒规则
 * date: 2023/3/21 15:50
 *
 * <AUTHOR>
 */
@Data
public class DeliveryAlertRuleDTO extends BaseObject {

    private static final long serialVersionUID = -8816675264896322069L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 配送仓编号
     */
    private Integer storeNo;

    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空")
    private String ruleName;

    /**
     * 品牌规则文件oss标识
     */
    private String brandRuleObjectOssKey;

    /**
     * 门店规则文件oss标识
     */
    private String merchantRuleObjectOssKey;

    /**
     * 品牌规则文件URL
     */
    private String brandRuleUrl;

    /**
     * 门店规则文件URL
     */
    private String merchantRuleUrl;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
