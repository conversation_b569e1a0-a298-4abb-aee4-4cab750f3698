package net.summerfarm.tms.alert.dto;

import lombok.Data;
import net.summerfarm.tms.alert.group.DeliveryAlertRuleValidationGroups;
import net.summerfarm.tms.anno.CityStoreNo;
import net.summerfarm.tms.base.BaseObject;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Description:配送提醒规则命令
 * date: 2023/3/21 15:59
 *
 * <AUTHOR>
 */
@Data
public class DeliveryAlertRuleCommand extends BaseObject {

    private static final long serialVersionUID = 3568027299417748192L;

    /**
     * 主键
     */
    @NotNull(message = "配送提醒规则ID不能为空", groups = {DeliveryAlertRuleValidationGroups.EditGroup.class})
    private Long id;

    /**
     *
     * 配送仓编号
     */
    @NotNull(message = "配送仓编号不能为空", groups = {DeliveryAlertRuleValidationGroups.AddGroup.class, DeliveryAlertRuleValidationGroups.EditGroup.class})
    @CityStoreNo
    private Integer storeNo;

    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空", groups = {DeliveryAlertRuleValidationGroups.AddGroup.class, DeliveryAlertRuleValidationGroups.EditGroup.class})
    @Length(max = 10, message = "规则名称已超出长度限制", groups = {DeliveryAlertRuleValidationGroups.AddGroup.class, DeliveryAlertRuleValidationGroups.EditGroup.class})
    private String ruleName;

    /**
     * 品牌规则文件oss标识
     */
    private String brandRuleObjectOssKey;

    /**
     * 门店规则文件oss标识
     */
    private String merchantRuleObjectOssKey;

}
