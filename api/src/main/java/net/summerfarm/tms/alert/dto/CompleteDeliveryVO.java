package net.summerfarm.tms.alert.dto;

import lombok.Data;
import net.summerfarm.tms.fence.dto.AdCodeMsgDTO;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> 2021/07/09
 */
@Data
public class CompleteDeliveryVO {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 物流中心编号（配送仓编号)
     */
    private Integer storeNo;

    /**
     * 配送完成时间
     */
    private String completeDeliveryTime;

    /**
     * 状态 0 正常 1 暂停
     */
    private Integer status;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDate updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDate createTime;

    /**
     * 城市
     */
    private String city;

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 行政区域
     */
    private List<AdCodeMsgDTO> areaAdCodeList;

    /**
     * 区域
     */
    private String region;

}
