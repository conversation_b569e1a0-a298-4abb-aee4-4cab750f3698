package net.summerfarm.tms.alert.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.tms.anno.CityStoreNo;

import java.util.List;

/**
 * Description:完成配送提醒命令
 * date: 2023/3/29 15:59
 *
 * <AUTHOR>
 */
@Data
public class CompleteDeliveryCommand {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 物流中心编号（配送仓编号)
     */
    @CityStoreNo
    private Integer storeNo;

    /**
     * 配送完成时间
     */
    private String completeDeliveryTime;

    /**
     * 区域编码集合
     */
    private List<String> adCodes;

    /**
     * 城市
     */
    private String city;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

}
