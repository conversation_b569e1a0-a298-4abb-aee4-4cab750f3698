package net.summerfarm.tms.service.dist.converter;

import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.client.message.in.DistOrderCancelMessage;
import net.summerfarm.tms.client.message.in.DistOrderCreateMessage;
import net.summerfarm.tms.client.message.in.DistOrderItemMessage;
import net.summerfarm.tms.client.message.in.DistSiteMessage;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.vo.DistItemVO;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 修改配送方式转换类
 * date: 2025/7/21 14:55<br/>
 *
 * <AUTHOR> />
 */
public class ChangeDistOrderFulfilmentWayConverter {


    public static DistOrderCancelMessage distOrderEntityToBuildCancelMsg(DistOrderEntity distOrder) {
        DistOrderCancelMessage distOrderCancelMessage = new DistOrderCancelMessage();
        distOrderCancelMessage.setOuterOrderId(distOrder.getDistClientVO().getOutOrderId());
        distOrderCancelMessage.setSource(distOrder.getSource().getCode());
        distOrderCancelMessage.setExpectBeginTime(distOrder.getDistFlowVO().getExpectBeginTime());
        distOrderCancelMessage.setOuterContactId(distOrder.getDistClientVO().getOutContactId());
        distOrderCancelMessage.setUpdater(distOrder.getUpdater() == null ? "OFC" : distOrder.getUpdater());
        distOrderCancelMessage.setUpdaterId(distOrder.getUpdaterId() == null ? "OFC" : distOrder.getUpdater());
        return distOrderCancelMessage;
    }

    public static DistOrderCreateMessage distOrderEntityToBuildDistOrderMsg(DistOrderEntity distOrder) {
        DistOrderCreateMessage distOrderCreateMessage = new DistOrderCreateMessage();

        distOrderCreateMessage.setOuterId(distOrder.getDistId().toString());
        distOrderCreateMessage.setType(distOrder.getDistFlowVO().getType());
        distOrderCreateMessage.setPickType(distOrder.getPickType().getCode());
        distOrderCreateMessage.setSource(distOrder.getSource().getCode());
        distOrderCreateMessage.setOuterOrderId(distOrder.getDistOrderMark().getOuterOrderId());
        distOrderCreateMessage.setOuterTenantId(distOrder.getDistClientVO().getOutTenantId());
        distOrderCreateMessage.setOuterBrandName(distOrder.getDistClientVO().getOutBrandName());
        distOrderCreateMessage.setOuterClientId(distOrder.getDistClientVO().getOutClientId());
        distOrderCreateMessage.setOuterClientName(distOrder.getDistClientVO().getOutClientName());
        distOrderCreateMessage.setOuterContactId(distOrder.getDistClientVO().getOutContactId());
        distOrderCreateMessage.setOuterRemark(distOrder.getCloseReason());
        SiteEntity beginSite = distOrder.getBeginSite();
        if (beginSite != null) {
            DistSiteMessage beginSiteMsg = new DistSiteMessage();

            beginSiteMsg.setOutBusinessNo(beginSite.getOutBusinessNo());
            beginSiteMsg.setProvince(beginSite.getProvince());
            beginSiteMsg.setCity(beginSite.getCity());
            beginSiteMsg.setArea(beginSite.getArea());
            beginSiteMsg.setAddress(beginSite.getAddress());
            beginSiteMsg.setPoi(beginSite.getPoi());
            beginSiteMsg.setPhone(beginSite.getPhone());
            beginSiteMsg.setName(beginSite.getName());
            beginSiteMsg.setType(beginSite.getType());

            distOrderCreateMessage.setBeginSite(beginSiteMsg);
        }

        SiteEntity endSite = distOrder.getEndSite();
        if (endSite != null) {
            DistSiteMessage endSiteMsg = new DistSiteMessage();

            endSiteMsg.setOutBusinessNo(endSite.getOutBusinessNo());
            endSiteMsg.setProvince(endSite.getProvince());
            endSiteMsg.setCity(endSite.getCity());
            endSiteMsg.setArea(endSite.getArea());
            endSiteMsg.setAddress(endSite.getAddress());
            endSiteMsg.setPoi(endSite.getPoi());
            endSiteMsg.setPhone(endSite.getPhone());
            endSiteMsg.setName(endSite.getName());
            endSiteMsg.setType(endSite.getType());

            distOrderCreateMessage.setEndSite(endSiteMsg);
        }
        distOrderCreateMessage.setBeginClientName(distOrder.getBeginClientName());
        distOrderCreateMessage.setExpectBeginTime(distOrder.getDistFlowVO().getExpectBeginTime());
        distOrderCreateMessage.setExpectEndTime(distOrder.getDistFlowVO().getExpectEndTime());
        distOrderCreateMessage.setTimeFrame(distOrder.getDistFlowVO().getTimeFrame());
        List<DistItemVO> distItems = distOrder.getDistItems();
        if (!CollectionUtils.isEmpty(distItems)) {
            List<DistOrderItemMessage> distOrderItemList = distItems.stream().map(item -> {
                DistOrderItemMessage distOrderItemMessage = new DistOrderItemMessage();

                distOrderItemMessage.setOuterItemId(item.getOutItemId());
                distOrderItemMessage.setOuterItemType(item.getOutItemType());
                distOrderItemMessage.setOuterItemName(item.getOutItemName());
                distOrderItemMessage.setOuterItemPrice(item.getOutItemPrice());
                distOrderItemMessage.setVolume(item.getVolume());
                distOrderItemMessage.setWeight(item.getWeight());
                distOrderItemMessage.setQuantity(item.getQuantity());
                distOrderItemMessage.setTemperature(item.getTemperatureEnum().getCode());
                distOrderItemMessage.setSpecification(item.getSpecification());
                distOrderItemMessage.setUnit(item.getUnit());
                distOrderItemMessage.setType(item.getType());
                distOrderItemMessage.setDeliveryType(item.getDeliveryType());
                distOrderItemMessage.setPackType(item.getPackType());

                return distOrderItemMessage;
            }).collect(Collectors.toList());

            distOrderCreateMessage.setDistOrderItemList(distOrderItemList);
        }

        distOrderCreateMessage.setCreator(distOrder.getCreator());
        distOrderCreateMessage.setCreatorId(distOrder.getCreatorId());
        distOrderCreateMessage.setAddressRemark(distOrder.getSendRemark());
        distOrderCreateMessage.setFulfillmentDeliveryWay(distOrder.getFulfillmentDeliveryWay());
        return distOrderCreateMessage;
    }
}
