package net.summerfarm.tms.inbound.controller.delivery.vo.trunk;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;

import java.util.List;

/**
 * 调度单
 */
@Data
public class TrunkDeliveryBatchCarVO extends BaseObject {

    private List<DeliveryBatchDTO> deliveryBatchDTOS;

    /**
     * 路线起点
     */
    private String beginSiteName;

    /**
     * 路线终点
     */
    private String endSiteName;
}
