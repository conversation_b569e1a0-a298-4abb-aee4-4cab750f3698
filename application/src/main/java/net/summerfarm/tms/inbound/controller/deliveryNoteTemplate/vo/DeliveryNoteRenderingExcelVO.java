package net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo;

import lombok.Data;

import java.util.List;

/**
 * Description: 配送单渲染Excel VO<br/>
 * date: 2024/12/9 15:24<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryNoteRenderingExcelVO {

    /**
     * 配送单名称
     *  Excel直接用这个占位符{deliveryNoteName}
     */
    private String deliveryNoteName;

    /**
     * 客户名称(门店名称)
     * Excel直接用这个占位符{merchantName}
     */
    private String merchantName;

    /**
     * 联系人名称
     * Excel直接用这个占位符{contactName}
     */
    private String contactName;

    /**
     * 联系人名称
     * Excel直接用这个占位符{contactPhone}
     */
    private String contactPhone;

    /**
     * 详细地址
     * Excel直接用这个占位符{detailAddress}
     */
    private String detailAddress;

    /**
     * 配送备注
     * Excel直接用这个占位符{sendRemark}
     */
    private String sendRemark;

    /**
     * 订单备注
     * Excel直接用这个占位符{orderRemark}
     */
    private String orderRemark;

    /**
     * 订单编号
     * Excel直接用这个占位符{orderNo}
     */
    private String orderNo;

    /**
     * 发货时间
     *  Excel直接用这个占位符{deliveryTime}
     */
    private String deliveryTime;

    /**
     * 品牌名称
     * Excel直接用这个占位符{brandName}
     */
    private String brandName;

    /**
     * 商品详情
     */
    private List<GoodsItemVO> goodsItemVOList;

    /**
     * 客户经理名称
     * Excel直接用这个占位符{bdName}
     */
    private String bdName;

    /**
     * 客户经理电话
     * Excel直接用这个占位符{bdPhone}
     */
    private String bdPhone;

    /**
     * 配送路线
     * Excel直接用这个占位符{deliveryPath}
     */
    private String deliveryPath;

    /**
     * 合计数量
     * Excel直接用这个占位符{.totalQuantity}
     */
    private Integer totalQuantity;

    /**
     * 合计斤数
     * Excel直接用这个占位符{.totalWeightInPounds}
     */
    private String totalWeightInPounds;

    /**
     * 合计小计
     * Excel直接用这个占位符{.totalSubtotal}
     */
    private String totalSubtotal;

    /**
     * 销售公司名称
     * Excel直接用这个占位符{sellingEntityName}
     */
    private String sellingEntityName;
}
