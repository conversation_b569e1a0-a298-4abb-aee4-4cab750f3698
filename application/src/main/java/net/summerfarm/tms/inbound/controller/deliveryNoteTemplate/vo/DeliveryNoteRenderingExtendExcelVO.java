package net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo;

import lombok.Data;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateEntity;

/**
 * Description: 配送单导出继承子类<br/>
 * date: 2025/1/21 10:56<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryNoteRenderingExtendExcelVO extends DeliveryNoteRenderingExcelVO{

    /**
     * 应用来源 1顺路达 2鲜沐 3saas 4外单
     */
    private Integer appSource;

    /**
     * 客户是否显示价格 ture 展示 false 不展示
     */
    private Boolean customerShowPriceFlag;

    /**
     * 配送单模板是否显示价格 ture 展示 false 不展示
     */
    private Boolean deliveryTemplateShowPriceFlag;

    /**
     * 实际是否显示价格 ture 展示 false 不展示
     */
    private Boolean realShowPriceFlag;

    /**
     * 配送单模板
     */
    private TmsDeliveryNoteTemplateEntity deliveryNoteTemplate;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 大客户ID
     */
    private Long bigCustomerId;

    /**
     * 门店ID
     */
    private Long merchantId;

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 模板文件路径
     */
    private String templatePath;
}
