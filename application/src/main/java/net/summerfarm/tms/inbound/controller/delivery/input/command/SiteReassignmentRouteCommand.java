package net.summerfarm.tms.inbound.controller.delivery.input.command;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Description: 点位改派路线
 * date: 2025/4/25 16:18<br/>
 *
 * <AUTHOR> />
 */
@Data
public class SiteReassignmentRouteCommand {

    /**
     * 站点点位ID
     */
    private Long deliverySiteId;

    @NotNull(message = "路线批次不能为空")
    private Long batchId;

    /**
     * 站点点位排序
     */
    private Integer sequence;
}
