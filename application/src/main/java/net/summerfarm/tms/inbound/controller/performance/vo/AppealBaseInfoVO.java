package net.summerfarm.tms.inbound.controller.performance.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 基本信息
 */
@Data
public class AppealBaseInfoVO {
    /**
     * primary key
     */
    private Long id;

    /**
     * 履约审核任务ID
     */
    private Long performanceReviewTaskId;

    /**
     * 申诉状态 :0待申诉 1申诉中 2申诉失败 3申诉成功 4已关闭
     */
    private Integer appealStatus;

    /**
     * 审核类型 0城配-出仓、1城配-签收、2干线-签收
     */
    private Integer reviewTaskType;

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 配送路线
     */
    private String pathCode;

    /**
     * 路线名称
     */
    private String pathName;

    /**
     * 配送日期
     */
    private LocalDate deliveryTime;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 司机号码
     */
    private String driverPhone;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车型
     */
    private String carType;

    /**
     * 车辆存储条件 0常温、1冷藏
     */
    private String carStorage;

    /**
     * 店铺名称
     */
    private String clientName;

    /**
     * 点位序号
     */
    private Integer sequence;

    /**
     * 判罚金额
     */
    private BigDecimal penaltyMoney;
}