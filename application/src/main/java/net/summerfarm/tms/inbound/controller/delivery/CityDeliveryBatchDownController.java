package net.summerfarm.tms.inbound.controller.delivery;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.aspect.Token;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.inbound.controller.delivery.input.DownloadPathInput;
import net.summerfarm.tms.inbound.controller.delivery.input.DownloadPathProductInput;
import net.summerfarm.tms.inbound.controller.delivery.input.DownloadPrePathInput;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;

/**
 * 城配下载文件接口
 */
@RestController
@RequestMapping("/tms-new/city-delivery-batch-down")
@Slf4j
public class CityDeliveryBatchDownController {
    @Resource
    private DeliveryBatchService deliveryBatchService;

    /**
     * 下载路线-接入下载中心
     */
    @PostMapping("/export-async/path")
    public TmsResult<Long> downloadPath(@RequestBody DownloadPathInput input) {
        return TmsResult.success(deliveryBatchService.downloadPath(input.getStoreNo(), input.getDeliveryTime()));
    }

    /**
     * 下载预排名单-接入下载中心
     */
    @PostMapping("/export-async/pre-path")
    public TmsResult<Long> downloadPrePath(@RequestBody DownloadPrePathInput input) {
        return TmsResult.success(deliveryBatchService.downloadPrePath(input.getStoreNo()));
    }

    /**
     * 上传预排名单
     */
    @PostMapping("/upsert/pre-path-upload")
    public TmsResult<Void> prePathUpload(@RequestParam("file") MultipartFile file) {
        return deliveryBatchService.prePathUpload(file);
    }

    /**
     * 下载路线商品-接入下载中心
     */
    @PostMapping("/export-async/path-product")
    public TmsResult<Long> downloadPathProduct(@RequestBody DownloadPathProductInput input) {
        return TmsResult.success(deliveryBatchService.downloadPathProduct(input.getStoreNo(), input.getDeliveryTime()));
    }

    /**
     * 出仓晚点监控
     */
    @GetMapping("/query/batch-out-time-monitor")
    public void batchOutTimeMonitor(String postDate) {
        deliveryBatchService.batchOutTimeMonitor(postDate);
    }

    /**
     * 出仓晚点监控数据下载
     */
    @GetMapping("/export/batch-out-time-down")
    public void batchOutTimeMonitorDown(String postDate, HttpServletResponse response) {
        deliveryBatchService.batchOutTimeMonitorDown(postDate,response);
    }
}
