package net.summerfarm.tms.inbound.controller.backdoor;

import com.alibaba.fastjson.JSON;
import com.aliyun.odps.simpleframework.xml.core.Validate;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.delivery.DeliveryBatchDomainService;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.dist.DistOrderDomainService;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.DistOrderService;
import net.summerfarm.tms.dist.dto.BatchChangeDistOrdersFulfillmentDeliveryWayDTO;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.input.command.BatchChangeDistOrdersFulfillmentDeliveryWayCommandInput;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.enums.DistOrderStatusEnum;
import net.summerfarm.tms.ext.AuthExtService;
import net.summerfarm.tms.inbound.controller.backdoor.input.BatchCloseDistOrderCommandInput;
import net.summerfarm.tms.inbound.controller.backdoor.input.BatchIntelligentDistanceCommandInput;
import net.summerfarm.tms.inbound.controller.backdoor.input.DistOrderDeliveryTimeChangeCommandInput;
import net.summerfarm.tms.local.delivery.DeliveryBatchDistanceHandleService;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.summerfarm.tms.service.backdoor.BackDoorService;
import net.xianmu.common.result.CommonResult;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:后门接口统一处理
 * date: 2024/2/4 18:03
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/tms-new/back-door")
@Slf4j
public class BackDoorController {

    @Resource
    private BackDoorService backDoorService;
    @Resource
    private AuthExtService authExtService;
    @Resource
    private DistOrderRepository distOrderRepository;
    @Resource
    private DistOrderDomainService distOrderDomainService;
    @Resource
    private DeliveryBatchService deliveryBatchService;
    @Resource
    private DeliveryBatchDistanceHandleService deliveryBatchDistanceHandleService;
    @Resource
    private DeliveryBatchDomainService deliveryBatchDomainService;
    @Resource
    private DistOrderService distOrderService;

    /**
     * 委托单批量更新配送日期
     */
    @PostMapping("/upsert/dist/batch-update-delivery-time")
    public TmsResult<Map<String, String>> batchChangeDeliveryTime(@RequestBody @Validate DistOrderDeliveryTimeChangeCommandInput input) {
        Map<String, String> resultMap = backDoorService.batchChangeDeliveryTime(input);
        if (!CollectionUtils.isEmpty(resultMap)){
            return TmsResult.fail(resultMap);
        }
        return TmsResult.success(null);
    }

    /**
     * 批量关闭承运单
     */
    @PostMapping("/upsert/batchClose")
    public TmsResult<Void> batchCloseDistOrder(@RequestBody @Validated BatchCloseDistOrderCommandInput command) {
        List<DistOrderEntity> distOrderEntities = distOrderRepository.queryList(DistOrderQuery.builder()
                .status(Collections.singletonList(DistOrderStatusEnum.TO_BE_WIRED.getCode()))
                .geExpectBeginTime(command.getBeginDeliveryDate())
                .leExpectBeginTime(command.getEndDeliveryDate())
                .sources(Lists.newArrayList(DistOrderSourceEnum.PURCHASE.getCode(), DistOrderSourceEnum.OWN_SALE_OUT.getCode(), DistOrderSourceEnum.ALL_CATEGORY_PICK.getCode()))
                .build());

        // 排除出提货用车 2024-01-01的承运单
        distOrderEntities = distOrderEntities.stream().filter(dist -> !(dist.getSource() == DistOrderSourceEnum.ALL_CATEGORY_PICK &&
                Objects.equals(dist.getDistFlowVO().getExpectBeginTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),"2024-01-01 00:00:00")))
                .collect(Collectors.toList());

        String updater = String.valueOf(authExtService.getCurrentUserName());
        String updaterId = String.valueOf(authExtService.getCurrentUserId());
        for (DistOrderEntity distOrderEntity : distOrderEntities) {
            if(DistOrderSourceEnum.getTrunkOuterSource().contains(distOrderEntity.getSource())){
                log.info("该委托单为外单不可关闭,distOrder:{}", JSON.toJSONString(distOrderEntity));
                continue;
            }
            if (!distOrderEntity.waitCarrierFlag()) {
                log.info("该委托单已承运，不可关闭,distOrder:{}", JSON.toJSONString(distOrderEntity));
                continue;
            }

            distOrderEntity.update(updater, updaterId);
            distOrderDomainService.closeDistOrder(distOrderEntity, command.getCloseReason());
        }

        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 更新站点配送顺序
     */
    @PostMapping("/upsert/delivery-site-sequence-ortools")
    public CommonResult<Void> updateDeliverySiteSequenceByOrTools(@RequestBody List<Long> batchIds) {
        backDoorService.updateDeliverySiteSequenceByOrTools(batchIds);
        return CommonResult.ok();
    }

    /**
     * 更新智能排线公里数
     */
    @PostMapping("/upsert/batch-intelligent-distance")
    public CommonResult<Void> updateBatchIntelligentDistance(@RequestBody @Validated BatchIntelligentDistanceCommandInput input) {
        Integer storeNo = input.getStoreNo();
        LocalDate deliveryTime = input.getDeliveryTime();
        List<Long> batchIds = input.getBatchIds();
        deliveryBatchService.backDoorUpdateBatchIntelligentDistance(storeNo,deliveryTime,batchIds);
        return CommonResult.ok();
    }


    /**
     * ortools智能排线后门
     */
    @PostMapping("/upsert/delivery-batch-ortools")
    public CommonResult<Void> updateDeliveryBatchSequenceByOrTools(@RequestBody List<Long> batchIds) {
        for (Long batchId : batchIds) {
            backDoorService.updateDeliveryBatchSequenceByOrTools(batchId);
        }
        return CommonResult.ok();
    }

    /**
     * 更新专车孤点公里数后门
     */
    @PostMapping("/upsert/calc-special-send-site-path-min-distance")
    public CommonResult<Void> updateBatchIntelligentDistance(Long batchId) {
        deliveryBatchDistanceHandleService.calcSpecialSendSitePathMinDistance(batchId);
        return CommonResult.ok();
    }

    /**
     * 后门 城配委托单不存在排线点位
     */
    @PostMapping("/upsert/city-dist-order-no-delivery-site-fix")
    public CommonResult<Void> cityDistOrderNoDeliverySiteFix(Long distId) {
        if(distId == null){
            return CommonResult.ok();
        }
        DistOrderEntity distOrderEntity = distOrderRepository.queryWithSiteWithItem(distId);

        // 委托单预排
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchDomainService.prePathBatchForDistOrder(distOrderEntity);

        // 批次路线的保存
        deliveryBatchDomainService.createDeliveryBatch(deliveryBatchEntity);

        return CommonResult.ok();
    }

    /**
     * 后门 修改配送方式
     */
    @PostMapping("/upsert/batchChangeDistOrdersFulfillmentDeliveryWay")
    public CommonResult<BatchChangeDistOrdersFulfillmentDeliveryWayDTO> batchChangeDistOrdersFulfillmentDeliveryWay(@RequestBody List<Long> distIds) {
        if(CollectionUtils.isEmpty(distIds)){
            return CommonResult.ok();
        }
        List<DistOrderEntity> distOrderEntities = distOrderRepository.queryValidListWithItemByIds(distIds);

        List<BatchChangeDistOrdersFulfillmentDeliveryWayCommandInput> inputs = distOrderEntities.stream().map(e -> {
            BatchChangeDistOrdersFulfillmentDeliveryWayCommandInput input = new BatchChangeDistOrdersFulfillmentDeliveryWayCommandInput();
            input.setDeliveryTime(e.getDistFlowVO().getExpectBeginTime().toLocalDate());
            input.setOutOrderId(e.getDistClientVO().getOutOrderId());
            input.setOuterContactId(e.getDistClientVO().getOutContactId());
            input.setSource(e.getSource().getCode());

            return input;
        }).collect(Collectors.toList());

        BatchChangeDistOrdersFulfillmentDeliveryWayDTO batchChangeDistOrdersFulfillmentDeliveryWayDTO = distOrderService.batchChangeDistOrdersFulfillmentDeliveryWay(inputs);

        return CommonResult.ok(batchChangeDistOrdersFulfillmentDeliveryWayDTO);
    }

}
