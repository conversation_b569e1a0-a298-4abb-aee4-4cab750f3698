package net.summerfarm.tms.inbound.controller.delivery.vo.city;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/9/6 14:26<br/>
 *
 * <AUTHOR> />
 */
@Data
public class LackApprovedPageVO {
    /**
     * 任务编号
     */
    private Long id;
    /**
     * 库存仓
     */
    private String areaNoStr;
    /**
     * 城配仓
     */
    private String storeNoStr;
    /**
     * 司机名称
     */
    private String driverName;
    /**
     *配送完成时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishTime;
    /**
     * 店铺名称
     */
    private String mname;
    /**
     * sku
     */
    private String sku;
    /**
     * 商品名称
     */
    private String pdName;
    /**
     * 缺货数量
     */
    private Integer lackNum;
    /**
     * 缺货金额
     */
    private BigDecimal money;
    /**
     * 总价
     */
    private BigDecimal totalMoney;
    /**
     * 备注
     */
    private String remark;
    /**
     * 最新申诉
     */
    private List<String> appealInfos;
    /**
     * 责任方
     */
    private String responsibleInfo;
    /**
     * 缺货类型1.总仓-少发;2.总仓-库存不足;3.总仓-发错货;4.司机-误操作;5.司机-配送丢失;6.干线运输破损;7.其它 8.干线-腐烂
     */
    private String lackType;
    /**
     * 状态 1 待核准 2待判责 3 已完成
     */
    private Integer state;
}
