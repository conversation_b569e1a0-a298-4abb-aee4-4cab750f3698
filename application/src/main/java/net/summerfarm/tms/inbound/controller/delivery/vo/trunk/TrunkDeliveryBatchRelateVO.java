package net.summerfarm.tms.inbound.controller.delivery.vo.trunk;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.tms.enums.DeliveryBatchStatusEnum;
import net.summerfarm.tms.enums.DeliveryBatchTypeEnum;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:调度单关联查询视图对象
 * date: 2023/8/11 10:55
 *
 * <AUTHOR>
 */
@Data
public class TrunkDeliveryBatchRelateVO implements Serializable {

    private static final long serialVersionUID = -1637005054820113267L;

    /**
     * 调度单号
     */
    private Long deliveryBatchId;
    /**
     * 履约时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deliveryTime;
    /**
     * @see DeliveryBatchStatusEnum
     * 状态
     * 10待排线,20待配送30配送中,40配送完成,50关闭
     */
    private Integer status;
    /**
     * 状态描述
     */
    private String statusDesc;
    /**
     * @see DeliveryBatchTypeEnum
     * 调度单类型
     * -1干线城配用车、0干线用车、1调拨用车、2采购用车、3大客户用车、4仓库用车、5城配用车
     */
    private Integer type;
    /**
     * 调度单类型描述
     */
    private String typeDesc;
    /**
     * 班次 0正常 1加班
     */
    private Integer classes;
    /**
     * 班次描述
     */
    private String classesDesc;
    /**
     * 线路名称
     */
    private String pathName;
}
