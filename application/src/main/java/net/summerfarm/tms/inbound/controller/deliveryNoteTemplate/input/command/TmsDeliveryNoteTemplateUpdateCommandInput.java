package net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.command;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024-12-09 14:00:38
 * @version 1.0
 *
 */
@Data
public class TmsDeliveryNoteTemplateUpdateCommandInput implements Serializable{
	/**
	 * primary key
	 */
	@NotNull(message = "id不能为空")
	private Long id;

	/**
	 * 配送单名称
	 */
	@NotBlank(message = "配送单名称不能为空")
	private String deliveryNoteName;

	/**
	 * 应用来源 1顺路达 2鲜沐 3saas 4外单
	 */
	@NotNull(message = "应用来源不能为空")
	private Integer appSource;

	/**
	 * 前端页面字符串
	 */
	@NotBlank(message = "前端页面字符串不能为空")
	private String frontPageStr;

	/**
	 * 展示价格标识 0展示价格 1不展示价格
	 */
	@NotBlank(message = "展示价格标识不能为空")
	private Integer showPriceFlag;

	/**
	 * 作用域 0默认 1租户 2大客户
	 */
	@NotBlank(message = "作用域不能为空")
	private Integer scopeType;

	/**
	 * 展示价格的oss url
	 */
	private String showPriceTemplateOssUrl;

	/**
	 * 不展示价格的oss url
	 */
	private String noShowPriceTemplateOssUrl;

	@NotEmpty(message ="配送单归属不能为空")
	@Valid
	private List<TmsDeliveryNoteTemplateBelongUpdateCommandInput> belongUpdateCommandInput;

}