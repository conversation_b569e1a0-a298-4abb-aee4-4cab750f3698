package net.summerfarm.tms.inbound.controller.delivery.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description:配送点位审核命令
 * date: 2024/1/26 18:54
 *
 * <AUTHOR>
 */
@Data
public class DeliverySiteAuditCommandInput implements Serializable {

    private static final long serialVersionUID = -603932364088946186L;

    /**
     * 运输点位ID
     */
    @NotNull(message = "运输点位ID不能为空")
    private Long deliverySiteId;

    /**
     * 省份
     */
    @NotBlank(message = "提交参数省不能为空")
    private String newProvince;

    /**
     * 城市
     */
    @NotBlank(message = "提交参数城市不能为空")
    private String newCity;

    /**
     * 区域
     */
    private String newArea;

    /**
     * 地址
     */
    private String newAddress;

    /**
     * 新poi
     */
    @NotBlank(message = "提交参数poi不能为空")
    private String newPoi;
}
