package net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024-12-09 14:00:38
 * @version 1.0
 *
 */
@Data
public class TmsDeliveryNoteTemplateDetailVO implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 配送单名称
	 */
	private String deliveryNoteName;

	/**
	 * 应用来源 1顺路达 2鲜沐 3saas 4外单
	 */
	private Integer appSource;

	/**
	 * 使用状态 0使用中 1停用
	 */
	private Integer useState;

	/**
	 * 最近操作人
	 */
	private String lastOperatorName;

	/**
	 * 前端页面字符串
	 */
	private String frontPageStr;

	/**
	 * 展示价格标识 0展示价格 1不展示价格
	 */
	private Integer showPriceFlag;

	/**
	 * 作用域 0默认 1租户 2大客户
	 */
	private Integer scopeType;

	/**
	 * 展示价格的oss url
	 */
	private String showPriceTemplateOssUrl;

	/**
	 * 不展示价格的oss url
	 */
	private String noShowPriceTemplateOssUrl;

	/**
	 * 配送单归属详情信息
	 */
	private List<TmsDeliveryNoteTemplateBelongVO> tmsDeliveryNoteTemplateBelongVOList;

}