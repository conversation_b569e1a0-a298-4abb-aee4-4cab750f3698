package net.summerfarm.tms.inbound.controller.delivery.input;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * Description:下载路线商品参数
 * date: 2024/2/22 14:03
 *
 * <AUTHOR>
 */
@Data
public class DownloadPathProductInput implements Serializable {

    private static final long serialVersionUID = 4914038785297035673L;
    /**
     * 城配仓编号
     */
    @NotBlank(message = "城配仓编号不能为空")
    private String storeNo;

    /**
     * 配送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "配送时间不能为空")
    private LocalDate deliveryTime;
}
