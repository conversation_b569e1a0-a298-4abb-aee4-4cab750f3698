package net.summerfarm.tms.inbound.controller.frontcacae;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.config.TmsNacosConfig;
import net.summerfarm.tms.inbound.controller.frontcacae.input.PathQueryInput;
import net.xianmu.common.result.CommonResult;
import net.xianmu.gaode.support.enums.DriverRoutPlanStrategyEnum;
import net.xianmu.gaode.support.enums.XMDriverRoutPlanStrategyEnum;
import net.xianmu.gaode.support.service.LbsGaoDeService;
import net.xianmu.gaode.support.service.input.DriverRoutPlanningInput;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 排线缓存
 * date: 2024/8/12 17:27<br/>
 *
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/tms-new/front-path-cache")
@Slf4j
public class PathCacheController {

    @Resource
    private LbsGaoDeService lbsGaoDeService;

    @Resource
    private TmsNacosConfig tmsNacosConfig;

    /**
     * 获取排线信息
     * @param input 请求
     * @return 结果
     */
    @PostMapping("/query/path")
    public CommonResult<String> queryPath(@RequestBody @Validated PathQueryInput input) {
        DriverRoutPlanningInput driverRoutPlanningInput = new DriverRoutPlanningInput();
        driverRoutPlanningInput.setDestination(input.getDestination());
        driverRoutPlanningInput.setOrigin(input.getOrigin());
        driverRoutPlanningInput.setWaypoints(input.getWaypoints());
        if(!tmsNacosConfig.queryGaodeMinDistanceStoreNos().contains(String.valueOf(input.getStoreNo()))){
            // 不在白名单走默认
            driverRoutPlanningInput.setStrategyEunm(DriverRoutPlanStrategyEnum.DISTANCE_FIRST);
        }else{
            // 组合策略，取最短
            driverRoutPlanningInput.setXmDriverRoutPlanStrategyEnum(XMDriverRoutPlanStrategyEnum.COMBINATION_STRATEGY);
        }
        String result = lbsGaoDeService.queryDriverRoutPlanningCompress(driverRoutPlanningInput);


        return CommonResult.ok(result);
    }

}
