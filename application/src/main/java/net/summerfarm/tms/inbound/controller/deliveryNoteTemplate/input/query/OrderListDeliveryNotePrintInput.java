package net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.query;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * Description: 配送单打印<br/>
 * date: 2025/1/17 11:04<br/>
 *
 * <AUTHOR> />
 */
@Data
public class OrderListDeliveryNotePrintInput {

    /**
     * 订单编号
     */
    @NotEmpty(message = "订单号集合不能为空")
    @Size(min = 1, max = 200, message = "订单号集合最小为1个,不能超过200个")
    private List<String> orderNos;

    /**
     * 打印方向
     * true:纵向 false:横向
     */
    private Boolean printDirection;
}
