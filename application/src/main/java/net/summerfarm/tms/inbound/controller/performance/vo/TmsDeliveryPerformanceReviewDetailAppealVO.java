package net.summerfarm.tms.inbound.controller.performance.vo;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-08-30 14:11:06
 * @version 1.0
 *
 */
@Data
public class TmsDeliveryPerformanceReviewDetailAppealVO implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;


	/**
	 * 履约审核任务ID
	 */
	private Long performanceReviewTaskId;

	/**
	 * 履约审核详情ID
	 */
	private Long delPerfDetailReviewId;

	/**
	 * 配送批次ID
	 */
	private Long deliveryBatchId;

	/**
	 * 状态0待申诉 1申诉中 2申诉失败 3申诉成功 4已关闭
	 */
	private Integer status;

	/**
	 * 城配仓编号
	 */
	private Integer storeNo;

	/**
	 * 城配仓名称
	 */
	private String storeName;



}