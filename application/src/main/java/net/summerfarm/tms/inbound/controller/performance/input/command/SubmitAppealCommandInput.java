package net.summerfarm.tms.inbound.controller.performance.input.command;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Description: 提交申诉<br/>
 * date: 2024/8/30 16:30<br/>
 *
 * <AUTHOR> />
 */
@Data
public class SubmitAppealCommandInput {

    /**
     * 申诉ID
     */
    @NotNull(message = "申诉ID不能为空")
    private Long appealId;

    @NotEmpty(message = "申诉项不能为空")
    @Valid
    private List<AppealItemCommandInput> appealItemCommandInputList;

}
