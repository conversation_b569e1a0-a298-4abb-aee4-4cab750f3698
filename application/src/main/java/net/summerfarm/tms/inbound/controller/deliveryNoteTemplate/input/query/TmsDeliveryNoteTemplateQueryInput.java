package net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.query;

import lombok.Data;
import java.io.Serializable;

import net.summerfarm.tms.base.AbstractPageQuery;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2024-12-09 14:00:38
 * @version 1.0
 *
 */
@Data
public class TmsDeliveryNoteTemplateQueryInput extends AbstractPageQuery implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 配送单名称
	 */
	private String deliveryNoteName;

	/**
	 * 应用来源 1顺路达 2鲜沐 3saas 4外单
	 */
	private Integer appSource;

	/**
	 * 使用状态 0使用中 1停用
	 */
	private Integer useState;

	/**
	 * 配送单归属业务名称
	 */
	private String belongBusinessName;


}