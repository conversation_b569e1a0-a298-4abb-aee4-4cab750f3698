package net.summerfarm.tms.inbound.controller.performance.input.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Description: 批量关闭申诉<br/>
 * date: 2024/8/30 16:30<br/>
 *
 * <AUTHOR> />
 */
@Data
public class BatchCloseAppealCommandInput {
    /**
     * 批量申诉ID
     */
    @NotEmpty(message = "申诉ID集合不能为空")
    private List<Long> appealIds;

    /**
     * 关闭原因
     */
    @NotBlank(message = "关闭原因不能为空")
    private String closeReason;

}
