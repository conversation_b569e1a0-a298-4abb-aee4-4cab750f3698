package net.summerfarm.tms.inbound.controller.performance.vo;

import lombok.Data;

/**
 * 申诉信息列表
 */
@Data
public class AppealItemVO {
    /**
     * 申诉项ID
     */
    private Long appealItemId;

    /**
     * 点位名称
     */
    private String siteName;

    /**
     * 详情地址
     */
    private String fullAddress;

    /**
     * 不合规图片类型
     */
    private String nonComplianceSitePicType;

    /**
     * 不合规图片
     */
    private String nonCompliancePic;

    /**
     * 不合规原因
     */
    private String nonComplianceReason;

    /**
     * 申诉图片
     */
    private String appealPic;

    /**
     * 申诉原因
     */
    private String appealReason;
}
