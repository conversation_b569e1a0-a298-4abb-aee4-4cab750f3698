package net.summerfarm.tms.inbound.controller.delivery.vo.trunk;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.tms.base.BaseObject;
import net.summerfarm.tms.delivery.dto.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 调度单
 */
@Data
public class TrunkDeliveryBatchVO extends BaseObject {
    /**
     * 调度单ID
     */
    private Long batchId;
    /**
     * 调度单状态
     */
    private Integer status;
    /**
     * 调度单状态描述
     */
    private String statusDesc;

    /**
     * 调度单类型
     * -1干线城配用车、0干线用车、1调拨用车、2采购用车、3大客户用车、4仓库用车、5城配用车
     */
    private Integer type;
    /**
     * 调度单类型描述
     */
    private String typeDesc;

    /**
     * 线路名称
     */
    private String pathName;
    /**
     * 承运商ID
     */
    private Long carrierId;
    /**
     * 承运商名称
     */
    private String carrierName;
    /**
     * 司机ID
     */
    private Long driverId;
    /**
     * 司机名称
     */
    private String driverName;
    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 车辆id
     */
    private Long carId;
    /**
     * 车牌号
     */
    private String carNum;
    /**
     * 车型
     */
    private Integer carType;
    /**
     * 车型描述
     */
    private String carTypeDesc;
    /**
     * 车辆存储条件
     */
    private String storageName;
    /**
     * 履约时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deliveryTime;
    /**
     * 承运时间(开始)
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;
    /**
     * 创单时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime creatTime;
    /**
     * 关闭原因
     */
    private String closeReason;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 运输路线
     */
    private List<DeliverySiteDTO> deliverySiteList;
    /**
     * 关联的承运单
     */
    private List<TrunkDeliveryDistOrderDTO> trunkDeliveryDistOrderDTOS;
    /**
     * 配送单信息
     */
    private List<DeliveryOrderDTO> deliveryOrderDTOList;
    /**
     * 总冷冻重量
     */
    private BigDecimal freezeWeightTotal;

    /**
     * 总冷冻体积
     */
    private BigDecimal freezeVolumeTotal;

    /**
     * 总冷冻件数
     */
    private Integer freezeQuantityTotal;

    /**
     * 总冷藏重量
     */
    private BigDecimal coldWeightTotal;

    /**
     * 总冷藏体积
     */
    private BigDecimal coldVolumeTotal;

    /**
     * 总冷藏件数
     */
    private Integer coldQuantityTotal;

    /**
     * 总常温重量
     */
    private BigDecimal normalWeightTotal;

    /**
     * 总常温体积
     */
    private BigDecimal normalVolumeTotal;

    /**
     * 总常温件数
     */
    private Integer normalQuantityTotal;
    /**
     * 总重量
     */
    private BigDecimal weightTotal;

    /**
     * 总体积
     */
    private BigDecimal volumeTotal;

    /**
     * 总件数
     */
    private Integer quantityTotal;

    /**
     * 调度单和当前承运单是否已关联 1已关联,0未关联
     */
    private Integer bindDist;
    /**
     * 预估费用
     */
    private BigDecimal estimateFare;
    /**
     * 区域
     */
    private String area;

    /**
     * 班次 0正常 1加班
     */
    private Integer classes;

    /**
     * 备注
     */
    private String remarkInfo;

    /**
     * 关闭人
     */
    private String closeUser;

    /**
     * 更新时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    /**
     * 总距离 km
     */
    private BigDecimal planTotalDistance;

    /**
     * 运费明细项集合
     */
    private List<DeliveryBatchFareDTO> deliveryBatchFareDTOList;

    /**
     * 体积装载率
     */
    private BigDecimal volumeLoadRatio ;

    /**
     * 重量装载率
     */
    private BigDecimal weightLoadRatio ;

    /**
     * 件数装载率
     */
    private BigDecimal quantityLoadRatio ;

    /**
     * 关联批次集合
     */
    private List<DeliveryBatchRelationDTO> deliveryBatchRelationDTOList;

    /**
     * 承运商品类型，0：标品，1：水果
     */
    private Integer carryType;
    /**
     * 承运商品类型描述
     */
    private String carryTypeDesc;
}
