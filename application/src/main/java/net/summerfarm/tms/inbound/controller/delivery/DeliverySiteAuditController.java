package net.summerfarm.tms.inbound.controller.delivery;

import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.constants.RedisConstants;
import net.summerfarm.tms.delivery.DeliverySiteService;
import net.summerfarm.tms.inbound.controller.delivery.input.DeliverySiteAuditCommandInput;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.inbound.controller.delivery.input.DeliverySiteIdQueryInput;
import net.summerfarm.tms.inbound.controller.delivery.vo.DeliverySiteAuditDetailVO;
import net.summerfarm.tms.inbound.converter.delivery.DeliverySiteAuditVoConverter;
import net.summerfarm.tms.service.delivery.DeliverySiteAuditCommandService;
import net.summerfarm.tms.service.delivery.DeliverySiteAuditQueryService;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Description:城配配送点位审核
 * date: 2024/1/31 18:22
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/tms-new/city-delivery-site-audit")
public class DeliverySiteAuditController {

    @Resource
    private DeliverySiteAuditQueryService deliverySiteAuditQueryService;

    @Resource
    private DeliverySiteAuditCommandService deliverySiteAuditCommandService;

    /**
     * 获取点位审核详情小程序端
     */
    @PostMapping("/query/detail")
    public TmsResult<DeliverySiteAuditDetailVO> queryDeliverySiteAuditDetail(@RequestBody @Validated DeliverySiteIdQueryInput queryInput){
        DeliverySiteDTO deliverySiteDTO = deliverySiteAuditQueryService.queryDeliverySiteAuditDetail(queryInput.getDeliverySiteId());
        return TmsResult.success(DeliverySiteAuditVoConverter.dto2Vo(deliverySiteDTO));
    }

    /**
     * 发起点位审核小程序端
     */
    @PostMapping("/upsert/create")
    @XmLock(prefixKey = RedisConstants.Delivery.TMS_DELIVERY_SITE_AUDIT, key = "{auditCommand.deliverySiteId}", message = "操作频繁，请稍后重试")
    public TmsResult<Void> deliverySiteAudit(@RequestBody @Validated DeliverySiteAuditCommandInput auditCommand){
        deliverySiteAuditCommandService.deliverySiteAudit(auditCommand);
        return TmsResult.VOID_SUCCESS;
    }
}
