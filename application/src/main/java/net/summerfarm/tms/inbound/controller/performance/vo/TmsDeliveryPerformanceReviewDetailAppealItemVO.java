package net.summerfarm.tms.inbound.controller.performance.vo;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-08-30 14:11:07
 * @version 1.0
 *
 */
@Data
public class TmsDeliveryPerformanceReviewDetailAppealItemVO implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 申诉ID
	 */
	private Long appealId;

	/**
	 * 配送点id
	 */
	private Long deliverySiteId;

	/**
	 * 点位id
	 */
	private Long siteId;

	/**
	 * 路线次序
	 */
	private Integer sequence;

	/**
	 * 详情地址
	 */
	private String siteAddress;

	/**
	 * 不合规图片类型
	 */
	private Integer nonComplianceSitePicType;

	/**
	 * 不合规图片
	 */
	private String nonCompliancePic;

	/**
	 * 不合规原因
	 */
	private String nonComplianceReason;

	/**
	 * 申诉图片
	 */
	private String appealPic;

	/**
	 * 申诉原因
	 */
	private String appealReson;



}