package net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo;

import lombok.Data;

/**
 * Description: 商品明细<br/>
 * date: 2024/12/9 15:31<br/>
 *
 * <AUTHOR> />
 */
@Data
public class GoodsItemVO {

    /**
     * 序号
     * Excel直接用这个占位符{.seq}
     */
    private String seq;

    /**
     * 商品名称
     * Excel直接用这个占位符{.itemName}
     */
    private String itemName;

    /**
     * 商品规格
     * Excel直接用这个占位符{.specification}
     */
    private String specification;

    /**
     * 温区
     * Excel直接用这个占位符{.temperature}
     */
    private String temperature;

    /**
     * 单位
     * Excel直接用这个占位符{.unit}
     */
    private String unit;


    /**
     * 数量
     * Excel直接用这个占位符{.quantity}
     */
    private String quantity;

    /**
     * 单价
     * Excel直接用这个占位符{.price}
     */
    public String price;

    /**
     * 斤数
     * Excel直接用这个占位符{.weightInPounds}
     */
    private String weightInPounds;

    /**
     * 小计
     * Excel直接用这个占位符{.subtotal}
     */
    private String subtotal;

    /**
     * id
     */
    private String itemId;
}
