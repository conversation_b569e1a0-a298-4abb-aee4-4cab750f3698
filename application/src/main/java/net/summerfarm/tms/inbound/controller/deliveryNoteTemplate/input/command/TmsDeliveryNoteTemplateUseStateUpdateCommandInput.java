package net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.command;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024-12-09 14:00:38
 * @version 1.0
 *
 */
@Data
public class TmsDeliveryNoteTemplateUseStateUpdateCommandInput implements Serializable{
	/**
	 * primary key
	 */
	@NotNull(message = "id不能为空")
	private Long id;

	/**
	 * 使用状态 0使用中 1停用
	 */
	@NotNull(message = "使用状态不能为空")
	private Integer useState;


}