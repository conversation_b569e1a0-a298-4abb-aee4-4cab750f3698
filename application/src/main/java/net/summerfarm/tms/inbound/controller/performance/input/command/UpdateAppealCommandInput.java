package net.summerfarm.tms.inbound.controller.performance.input.command;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Description: 更新申诉<br/>
 * date: 2024/8/30 16:30<br/>
 *
 * <AUTHOR> />
 */
@Data
public class UpdateAppealCommandInput {
    /**
     * 申诉ID
     */
    @NotNull(message = "申诉ID不能为空")
    private Long appealId;

    /**
     * 状态0待申诉 1申诉中 2申诉失败 3申诉成功 4已关闭
     */
    private Integer status;

    /**
     * 申诉失败原因
     */
    private String appealFailReason;
}
