package net.summerfarm.tms.inbound.controller.delivery.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * Description:配送点位审核详情
 * date: 2024/1/26 18:35
 *
 * <AUTHOR>
 */
@Data
public class DeliverySiteAuditDetailVO implements Serializable {

    private static final long serialVersionUID = -7512566552353017002L;

    /**
     * 运输点位ID
     */
    private Long deliverySiteId;

    /**
     * 点位审核状态,0:待审核, 1:审核通过,2:拒绝重新交,3:审核失败
     */
    private Integer siteAuditStatus;

}
