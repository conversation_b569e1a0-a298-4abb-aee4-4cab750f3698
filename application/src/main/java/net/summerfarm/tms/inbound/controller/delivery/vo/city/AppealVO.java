package net.summerfarm.tms.inbound.controller.delivery.vo.city;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/9/6 15:46<br/>
 *
 * <AUTHOR> />
 */
@Data
public class AppealVO {
    /**
     * 申诉方:1城配、2干线、3仓库
     */
    private Integer side;
    /**
     * 申诉说明
     */
    private String description;
    /**
     * 申诉凭证
     */
    private String certificate;
    /**
     * 申诉人名称
     */
    private String appealPeopleName;

    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
