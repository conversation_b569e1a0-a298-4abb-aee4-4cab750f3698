package net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.query;

import lombok.Data;
import java.io.Serializable;
import net.xianmu.common.input.BasePageInput;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-12-09 14:00:38
 * @version 1.0
 *
 */
@Data
public class TmsDeliveryNoteTemplateBelongQueryInput extends BasePageInput implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 配送单模版ID
	 */
	private Long deliveryNoteTemplateId;

	/**
	 * 租户ID
	 */
	private Long tenantId;

	/**
	 * 作用域 0默认 1租户 2大客户
	 */
	private Integer scopeType;

	/**
	 * 作用域下的业务ID 0默认
	 */
	private String scopeBusinessId;

	/**
	 * 业务方名称
	 */
	private String scopeBusinessName;

	/**
	 * 展示价格标识 0展示价格 1不展示价格
	 */
	private Integer showPriceFlag;

	/**
	 * 模版OSS的URL
	 */
	private String templateOssUrl;



}