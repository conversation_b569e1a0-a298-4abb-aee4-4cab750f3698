package net.summerfarm.tms.inbound.controller.performance.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 操作记录
 */
@Data
public class AppealTrackLogVO {
    /**
     * create time
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 动作名称
     */
    private String actionName;

    /**
     * 操作人
     */
    private String operater;

    /**
     * 备注
     */
    private String remark;
}