package net.summerfarm.tms.inbound.controller.wechatMiniProgram.input.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 是否需要上传保温措施图片
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NeedUploadKeepTemperatureMethodPicQueryInput {

    /**
     * 配送批次id
     */
    @NotNull(message = "配送批次id不能为空")
    private Long batchId;
}
