package net.summerfarm.tms.inbound.controller.delivery.vo.trunk;

import lombok.Data;

import java.io.Serializable;

/**
 * Description:干线用车承运商品类型
 * date: 2023/11/3 10:42
 *
 * <AUTHOR>
 */
@Data
public class TrunkBatchCarryTypeVO implements Serializable {

    private static final long serialVersionUID = 242663617850440826L;

    /**
     * 承运商品类型
     */
    private Integer code;

    /**
     * 承运商品类型描述
     */
    private String desc;
}
