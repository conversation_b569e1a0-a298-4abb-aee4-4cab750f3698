package net.summerfarm.tms.inbound.controller.performance.assembler;


import net.summerfarm.tms.inbound.controller.performance.vo.*;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealItemEntity;
import net.summerfarm.tms.performance.entity.dataobject.ReviewDetailAppealPageObj;
import net.summerfarm.tms.track.entity.TmsTrackLogEntity;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2024-08-30 14:11:06
 * @version 1.0
 *
 */
public class TmsDeliveryPerformanceReviewDetailAppealAssembler {

    private TmsDeliveryPerformanceReviewDetailAppealAssembler() {
        // 无需实现
    }

/*

// ------------------------------- request ----------------------------
    public static TmsDeliveryPerformanceReviewDetailAppealQueryParam toTmsDeliveryPerformanceReviewDetailAppealQueryParam(TmsDeliveryPerformanceReviewDetailAppealQueryInput tmsDeliveryPerformanceReviewDetailAppealQueryInput) {
        if (tmsDeliveryPerformanceReviewDetailAppealQueryInput == null) {
            return null;
        }
        TmsDeliveryPerformanceReviewDetailAppealQueryParam tmsDeliveryPerformanceReviewDetailAppealQueryParam = new TmsDeliveryPerformanceReviewDetailAppealQueryParam();
        tmsDeliveryPerformanceReviewDetailAppealQueryParam.setId(tmsDeliveryPerformanceReviewDetailAppealQueryInput.getId());
        tmsDeliveryPerformanceReviewDetailAppealQueryParam.setCreateTime(tmsDeliveryPerformanceReviewDetailAppealQueryInput.getCreateTime());
        tmsDeliveryPerformanceReviewDetailAppealQueryParam.setUpdateTime(tmsDeliveryPerformanceReviewDetailAppealQueryInput.getUpdateTime());
        tmsDeliveryPerformanceReviewDetailAppealQueryParam.setPerformanceReviewTaskId(tmsDeliveryPerformanceReviewDetailAppealQueryInput.getPerformanceReviewTaskId());
        tmsDeliveryPerformanceReviewDetailAppealQueryParam.setDelPerfDetailReviewId(tmsDeliveryPerformanceReviewDetailAppealQueryInput.getDelPerfDetailReviewId());
        tmsDeliveryPerformanceReviewDetailAppealQueryParam.setDeliveryBatchId(tmsDeliveryPerformanceReviewDetailAppealQueryInput.getDeliveryBatchId());
        tmsDeliveryPerformanceReviewDetailAppealQueryParam.setStatus(tmsDeliveryPerformanceReviewDetailAppealQueryInput.getStatus());
        tmsDeliveryPerformanceReviewDetailAppealQueryParam.setStoreNo(tmsDeliveryPerformanceReviewDetailAppealQueryInput.getStoreNo());
        tmsDeliveryPerformanceReviewDetailAppealQueryParam.setStoreName(tmsDeliveryPerformanceReviewDetailAppealQueryInput.getStoreName());
        tmsDeliveryPerformanceReviewDetailAppealQueryParam.setPageIndex(tmsDeliveryPerformanceReviewDetailAppealQueryInput.getPageIndex());
        tmsDeliveryPerformanceReviewDetailAppealQueryParam.setPageSize(tmsDeliveryPerformanceReviewDetailAppealQueryInput.getPageSize());
        return tmsDeliveryPerformanceReviewDetailAppealQueryParam;
    }





    public static TmsDeliveryPerformanceReviewDetailAppealCommandParam buildCreateParam(TmsDeliveryPerformanceReviewDetailAppealCommandInput tmsDeliveryPerformanceReviewDetailAppealCommandInput) {
        if (tmsDeliveryPerformanceReviewDetailAppealCommandInput == null) {
            return null;
        }
        TmsDeliveryPerformanceReviewDetailAppealCommandParam tmsDeliveryPerformanceReviewDetailAppealCommandParam = new TmsDeliveryPerformanceReviewDetailAppealCommandParam();
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setId(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getId());
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setCreateTime(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getCreateTime());
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setUpdateTime(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getUpdateTime());
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setPerformanceReviewTaskId(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getPerformanceReviewTaskId());
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setDelPerfDetailReviewId(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getDelPerfDetailReviewId());
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setDeliveryBatchId(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getDeliveryBatchId());
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setStatus(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getStatus());
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setStoreNo(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getStoreNo());
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setStoreName(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getStoreName());
        return tmsDeliveryPerformanceReviewDetailAppealCommandParam;
    }


    public static TmsDeliveryPerformanceReviewDetailAppealCommandParam buildUpdateParam(TmsDeliveryPerformanceReviewDetailAppealCommandInput tmsDeliveryPerformanceReviewDetailAppealCommandInput) {
        if (tmsDeliveryPerformanceReviewDetailAppealCommandInput == null) {
            return null;
        }
        TmsDeliveryPerformanceReviewDetailAppealCommandParam tmsDeliveryPerformanceReviewDetailAppealCommandParam = new TmsDeliveryPerformanceReviewDetailAppealCommandParam();
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setId(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getId());
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setCreateTime(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getCreateTime());
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setUpdateTime(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getUpdateTime());
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setPerformanceReviewTaskId(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getPerformanceReviewTaskId());
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setDelPerfDetailReviewId(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getDelPerfDetailReviewId());
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setDeliveryBatchId(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getDeliveryBatchId());
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setStatus(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getStatus());
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setStoreNo(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getStoreNo());
        tmsDeliveryPerformanceReviewDetailAppealCommandParam.setStoreName(tmsDeliveryPerformanceReviewDetailAppealCommandInput.getStoreName());
        return tmsDeliveryPerformanceReviewDetailAppealCommandParam;
    }




// ------------------------------- response ----------------------------

    public static List<TmsDeliveryPerformanceReviewDetailAppealVO> toTmsDeliveryPerformanceReviewDetailAppealVOList(List<DeliveryPerformanceReviewDetailAppealEntity> tmsDeliveryPerformanceReviewDetailAppealEntityList) {
        if (tmsDeliveryPerformanceReviewDetailAppealEntityList == null) {
            return Collections.emptyList();
        }
        List<TmsDeliveryPerformanceReviewDetailAppealVO> tmsDeliveryPerformanceReviewDetailAppealVOList = new ArrayList<>();
        for (DeliveryPerformanceReviewDetailAppealEntity tmsDeliveryPerformanceReviewDetailAppealEntity : tmsDeliveryPerformanceReviewDetailAppealEntityList) {
            tmsDeliveryPerformanceReviewDetailAppealVOList.add(toTmsDeliveryPerformanceReviewDetailAppealVO(tmsDeliveryPerformanceReviewDetailAppealEntity));
        }
        return tmsDeliveryPerformanceReviewDetailAppealVOList;
}


   public static TmsDeliveryPerformanceReviewDetailAppealVO toTmsDeliveryPerformanceReviewDetailAppealVO(DeliveryPerformanceReviewDetailAppealEntity tmsDeliveryPerformanceReviewDetailAppealEntity) {
       if (tmsDeliveryPerformanceReviewDetailAppealEntity == null) {
            return null;
       }
       TmsDeliveryPerformanceReviewDetailAppealVO tmsDeliveryPerformanceReviewDetailAppealVO = new TmsDeliveryPerformanceReviewDetailAppealVO();
       tmsDeliveryPerformanceReviewDetailAppealVO.setId(tmsDeliveryPerformanceReviewDetailAppealEntity.getId());
       tmsDeliveryPerformanceReviewDetailAppealVO.setCreateTime(tmsDeliveryPerformanceReviewDetailAppealEntity.getCreateTime());
       tmsDeliveryPerformanceReviewDetailAppealVO.setUpdateTime(tmsDeliveryPerformanceReviewDetailAppealEntity.getUpdateTime());
       tmsDeliveryPerformanceReviewDetailAppealVO.setPerformanceReviewTaskId(tmsDeliveryPerformanceReviewDetailAppealEntity.getPerformanceReviewTaskId());
       tmsDeliveryPerformanceReviewDetailAppealVO.setDelPerfDetailReviewId(tmsDeliveryPerformanceReviewDetailAppealEntity.getDelPerfDetailReviewId());
       tmsDeliveryPerformanceReviewDetailAppealVO.setDeliveryBatchId(tmsDeliveryPerformanceReviewDetailAppealEntity.getDeliveryBatchId());
       tmsDeliveryPerformanceReviewDetailAppealVO.setStatus(tmsDeliveryPerformanceReviewDetailAppealEntity.getStatus());
       tmsDeliveryPerformanceReviewDetailAppealVO.setStoreNo(tmsDeliveryPerformanceReviewDetailAppealEntity.getStoreNo());
       tmsDeliveryPerformanceReviewDetailAppealVO.setStoreName(tmsDeliveryPerformanceReviewDetailAppealEntity.getStoreName());
       return tmsDeliveryPerformanceReviewDetailAppealVO;
   }
*/

    public static DeliveryPerformanceReviewDetailAppealPageVO toDeliveryPerformanceReviewDetailAppealPageVO(ReviewDetailAppealPageObj reviewDetailAppealPageObj) {
        if(reviewDetailAppealPageObj == null){
            return null;
        }
        DeliveryPerformanceReviewDetailAppealPageVO deliveryPerformanceReviewDetailAppealPageVO = new DeliveryPerformanceReviewDetailAppealPageVO();
        deliveryPerformanceReviewDetailAppealPageVO.setId(reviewDetailAppealPageObj.getId());
        deliveryPerformanceReviewDetailAppealPageVO.setPerformanceReviewTaskId(reviewDetailAppealPageObj.getPerformanceReviewTaskId());
        deliveryPerformanceReviewDetailAppealPageVO.setDeliveryTime(reviewDetailAppealPageObj.getDeliveryTime());
        deliveryPerformanceReviewDetailAppealPageVO.setStoreName(reviewDetailAppealPageObj.getStoreName());
        deliveryPerformanceReviewDetailAppealPageVO.setPathCode(reviewDetailAppealPageObj.getPathCode());
        deliveryPerformanceReviewDetailAppealPageVO.setPathName(reviewDetailAppealPageObj.getPathName());
        deliveryPerformanceReviewDetailAppealPageVO.setReviewTaskType(reviewDetailAppealPageObj.getReviewTaskType());
        deliveryPerformanceReviewDetailAppealPageVO.setDriverName(reviewDetailAppealPageObj.getDriverName());
        deliveryPerformanceReviewDetailAppealPageVO.setDriverPhone(reviewDetailAppealPageObj.getDriverPhone());
        deliveryPerformanceReviewDetailAppealPageVO.setCarNumber(reviewDetailAppealPageObj.getCarNumber());
        deliveryPerformanceReviewDetailAppealPageVO.setCarType(reviewDetailAppealPageObj.getCarType());
        deliveryPerformanceReviewDetailAppealPageVO.setCarStorage(reviewDetailAppealPageObj.getCarStorage());
        deliveryPerformanceReviewDetailAppealPageVO.setClientName(reviewDetailAppealPageObj.getOuterClientName());
        deliveryPerformanceReviewDetailAppealPageVO.setSequence(reviewDetailAppealPageObj.getSequence());
        deliveryPerformanceReviewDetailAppealPageVO.setCreateTime(reviewDetailAppealPageObj.getCreateTime());
        deliveryPerformanceReviewDetailAppealPageVO.setAppealStatus(reviewDetailAppealPageObj.getStatus());
        return deliveryPerformanceReviewDetailAppealPageVO;
    }


    public static AppealDetailVO toAppealDetailVO(ReviewDetailAppealPageObj reviewDetailAppealPageObj,
                                                  List<DeliveryPerformanceReviewDetailAppealItemEntity> appealItemEntityList,
                                                  List<TmsTrackLogEntity> tmsTrackLogEntities) {
        AppealDetailVO appealDetaillVO = new AppealDetailVO();
        appealDetaillVO.setAppealBaseInfoVO(TmsDeliveryPerformanceReviewDetailAppealAssembler.toAppealBaseInfoVO(reviewDetailAppealPageObj));
        if(!CollectionUtils.isEmpty(appealItemEntityList)){
            appealDetaillVO.setAppealItemVOList(appealItemEntityList.stream().map(TmsDeliveryPerformanceReviewDetailAppealAssembler::toAppealItemVO).collect(Collectors.toList()));
        }
        if(!CollectionUtils.isEmpty(tmsTrackLogEntities)){
            appealDetaillVO.setAppealTrackLogVOList(tmsTrackLogEntities.stream().map(TmsDeliveryPerformanceReviewDetailAppealAssembler::toAppealTrackLogVO).collect(Collectors.toList()));
        }
        return appealDetaillVO;
    }

    public static AppealBaseInfoVO toAppealBaseInfoVO(ReviewDetailAppealPageObj reviewDetailAppealPageObj){
        if(reviewDetailAppealPageObj == null){
            return null;
        }
        AppealBaseInfoVO appealBaseInfoVO = new AppealBaseInfoVO();
        appealBaseInfoVO.setId(reviewDetailAppealPageObj.getId());
        appealBaseInfoVO.setPerformanceReviewTaskId(reviewDetailAppealPageObj.getPerformanceReviewTaskId());
        appealBaseInfoVO.setDeliveryTime(reviewDetailAppealPageObj.getDeliveryTime());
        appealBaseInfoVO.setStoreName(reviewDetailAppealPageObj.getStoreName());
        appealBaseInfoVO.setPathCode(reviewDetailAppealPageObj.getPathCode());
        appealBaseInfoVO.setPathName(reviewDetailAppealPageObj.getPathName());
        appealBaseInfoVO.setReviewTaskType(reviewDetailAppealPageObj.getReviewTaskType());
        appealBaseInfoVO.setDriverName(reviewDetailAppealPageObj.getDriverName());
        appealBaseInfoVO.setDriverPhone(reviewDetailAppealPageObj.getDriverPhone());
        appealBaseInfoVO.setCarNumber(reviewDetailAppealPageObj.getCarNumber());
        appealBaseInfoVO.setCarType(reviewDetailAppealPageObj.getCarType());
        appealBaseInfoVO.setCarStorage(reviewDetailAppealPageObj.getCarStorage());
        appealBaseInfoVO.setClientName(reviewDetailAppealPageObj.getOuterClientName());
        appealBaseInfoVO.setSequence(reviewDetailAppealPageObj.getSequence());
        appealBaseInfoVO.setPenaltyMoney(reviewDetailAppealPageObj.getPenaltyMoney());
        appealBaseInfoVO.setAppealStatus(reviewDetailAppealPageObj.getStatus());

        return appealBaseInfoVO;
    }

    public static AppealItemVO toAppealItemVO(DeliveryPerformanceReviewDetailAppealItemEntity appealItemEntity){
        if(appealItemEntity == null){
            return null;
        }
        AppealItemVO appealItemVO = new AppealItemVO();
        appealItemVO.setAppealItemId(appealItemEntity.getId());
        //appealItemVO.setSiteName(appealItemEntity.getSiteAddress());
        appealItemVO.setFullAddress(appealItemEntity.getSiteAddress());
        appealItemVO.setNonComplianceSitePicType(appealItemEntity.getNonComplianceSitePicType());
        appealItemVO.setNonCompliancePic(appealItemEntity.getNonCompliancePic());
        appealItemVO.setNonComplianceReason(appealItemEntity.getNonComplianceReason());
        appealItemVO.setAppealPic(appealItemEntity.getAppealPic());
        appealItemVO.setAppealReason(appealItemEntity.getAppealReason());
        return appealItemVO;
    }

    public static AppealTrackLogVO toAppealTrackLogVO(TmsTrackLogEntity tmsTrackLogEntity){
        if(tmsTrackLogEntity == null){
            return null;
        }
        AppealTrackLogVO appealTrackLogVO = new AppealTrackLogVO();
        appealTrackLogVO.setCreateTime(tmsTrackLogEntity.getCreateTime());
        appealTrackLogVO.setActionName(tmsTrackLogEntity.getActionName());
        appealTrackLogVO.setOperater(tmsTrackLogEntity.getOperater());
        appealTrackLogVO.setRemark(tmsTrackLogEntity.getRemark());
        return appealTrackLogVO;
    }
}
