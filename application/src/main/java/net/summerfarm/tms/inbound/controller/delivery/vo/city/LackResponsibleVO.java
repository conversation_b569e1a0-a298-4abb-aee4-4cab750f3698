package net.summerfarm.tms.inbound.controller.delivery.vo.city;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Description: <br/>
 * date: 2023/9/6 15:28<br/>
 *
 * <AUTHOR> />
 */
@Data
public class LackResponsibleVO {

    /**
     * 责任方 1仓库 2城配 3无法判责 4干线
     */
    private Integer responsible;
    /**
     * 是否买赔 0是 1否
     */
    private Integer buyOut;
    /**
     * 买赔金额
     */
    private BigDecimal buyOutMoney;
}
