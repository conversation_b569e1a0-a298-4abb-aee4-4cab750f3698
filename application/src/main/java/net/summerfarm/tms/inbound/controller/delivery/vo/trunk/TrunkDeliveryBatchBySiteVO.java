package net.summerfarm.tms.inbound.controller.delivery.vo.trunk;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import java.util.List;

/**
 * 调度单
 */
@Data
public class TrunkDeliveryBatchBySiteVO extends BaseObject {
    /**
     * 承运单ID(委托单)
     */
    Long distOrderId;
    /**
     * 线路名称
     */
    String pathName;
    /**
     * 开始点位
     */
    Long beginSiteId;
    /**
     * 结束点位
     */
    Long endSiteId;
    /**
     * 需要绑定/可以绑定 调度单(批次)
     */
    List<TrunkDeliveryBatchVO> batchList;
}
