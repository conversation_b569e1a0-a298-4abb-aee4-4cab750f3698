package net.summerfarm.tms.inbound.controller.delivery.input;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * Description:下载预排路线参数
 * date: 2024/2/22 14:03
 *
 * <AUTHOR>
 */
@Data
public class DownloadPrePathInput implements Serializable {

    private static final long serialVersionUID = 6650914363981010160L;

    /**
     * 城配仓编号
     */
    @NotBlank(message = "城配仓编号不能为空")
    private String storeNo;

}
