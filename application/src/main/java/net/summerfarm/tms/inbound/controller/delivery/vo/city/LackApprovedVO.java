package net.summerfarm.tms.inbound.controller.delivery.vo.city;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/10/21 16:24<br/>
 *
 * <AUTHOR> />
 */
@Data
public class LackApprovedVO {
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     *缺货类型1.总仓-少发;2.总仓-库存不足;3.总仓-发错货;4.司机-误操作;5.司机-配送丢失;6.干线运输破损;7.其它
     */
    @NotNull(message = "lackTypes不能为空")
    private List<Integer> lackTypes;

    /**
     * 仓库编号
     */
    @NotNull(message = "storeNo.not.null")
    private Integer storeNo;

    private Integer stockLackNum;

    private String pic;

    private String remark;
}
