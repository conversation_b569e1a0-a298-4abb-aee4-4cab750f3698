package net.summerfarm.tms.inbound.controller.backdoor.input;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description:委托单配送时间变更
 * date: 2024/2/4 18:29
 *
 * <AUTHOR>
 */
@Data
public class DistOrderDeliveryTimeChangeCommandInput implements Serializable {

    private static final long serialVersionUID = -8448607084207944602L;

    /**
     * 鲜沐、SAAS普通订单集合(订单号唯一)
     */
    private List<String> normalOuterOrderIds;

    /**
     * 其他订单集合(订单号不唯一)
     */
    @Valid
    private List<DistOrderUkQueryInput> otherDistOrders;

    /**
     * 更新后的期望开始时间
     */
    @NotNull(message = "新配送时间不能为空")
    private LocalDateTime newExpectBeginTime;
}
