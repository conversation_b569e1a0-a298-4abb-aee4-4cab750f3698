package net.summerfarm.tms.inbound.controller.performance.vo;

import lombok.Data;
import java.util.List;

/**
 * Description: 申诉详情<br/>
 * date: 2024/8/30 16:01<br/>
 *
 * <AUTHOR> />
 */
@Data
public class AppealDetailVO {

    /**
     * 基本信息
     */
    private AppealBaseInfoVO appealBaseInfoVO;

    /**
     * 申诉信息列表
     */
    private List<AppealItemVO> appealItemVOList;

    /**
     * 操作记录
     */
    private List<AppealTrackLogVO> appealTrackLogVOList;
}


