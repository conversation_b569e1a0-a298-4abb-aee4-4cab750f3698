package net.summerfarm.tms.inbound.controller.performance.input.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class AppealItemCommandInput {
    /**
     * 申诉项ID
     */
    @NotNull(message = "申诉项ID不能为空")
    private Long appealItemId;

    /**
     * 申诉图片
     */
    private String appealPic;

    /**
     * 申诉原因
     */
    @NotBlank(message = "申诉原因不能为空")
    private String appealReason;

}