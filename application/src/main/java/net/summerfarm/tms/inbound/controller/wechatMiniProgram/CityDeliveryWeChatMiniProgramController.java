package net.summerfarm.tms.inbound.controller.wechatMiniProgram;

import net.summerfarm.tms.constants.RedisConstants;
import net.summerfarm.tms.inbound.controller.wechatMiniProgram.input.command.UploadKeepTemperatureMethodPicCommandInput;
import net.summerfarm.tms.inbound.controller.wechatMiniProgram.input.query.NeedUploadKeepTemperatureMethodPicQueryInput;
import net.summerfarm.tms.service.delivery.CityDeliveryQueryService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 城配微信小程序
 */
@RestController
@RequestMapping("/tms-new/city-wechat-mini-program")
public class CityDeliveryWeChatMiniProgramController {

    @Resource
    private CityDeliveryQueryService cityDeliveryQueryService;

    /**
     * 查询是否需要上传保温措施图片
     * @param input 入参
     * @return 是否需要
     */
    @PostMapping(value="/query/need-upload-keep-temperature-method-pic")
    public CommonResult<Boolean> queryNeedUploadKeepTemperatureMethodPic(@RequestBody @Validated NeedUploadKeepTemperatureMethodPicQueryInput input) {
        return CommonResult.ok(cityDeliveryQueryService.queryNeedUploadKeepTemperatureMethodPic(input));
    }

    /**
     * 上传保温措施图片
     * @param input 入参
     */
    @PostMapping(value="/upsert/upload-keep-temperature-method-pic")
    @XmLock(prefixKey = RedisConstants.Delivery.UPLOAD_KEEP_TEMPERATURE_METHOD_PICS, key = "{input.batchId}",waitTime = 1000, message = "操作频繁，请稍后重试")
    public CommonResult<Void> uploadKeepTemperatureMethodPic(@RequestBody @Validated UploadKeepTemperatureMethodPicCommandInput input) {
        cityDeliveryQueryService.uploadKeepTemperatureMethodPic(input);
        return CommonResult.ok();
    }
}
