package net.summerfarm.tms.inbound.controller.frontcacae.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * Description: <br/>
 * date: 2024/8/12 17:34<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PathCacheCommandInput {

    /**
     * 高德URL参数请求
     */
    @NotBlank(message = "高德URL参数请求不能为空")
    private String gaoDeUrlParam;


    /**
     * 高德响应结果
     */
    @NotBlank(message = "高德响应结果不能为空")
    private String respResult;

}
