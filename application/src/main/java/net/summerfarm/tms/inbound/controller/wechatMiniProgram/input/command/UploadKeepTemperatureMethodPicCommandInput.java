package net.summerfarm.tms.inbound.controller.wechatMiniProgram.input.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 上传保温措施图片
 * <AUTHOR>
 */
@Data
public class UploadKeepTemperatureMethodPicCommandInput {

    /**
     * 配送批次id
     */
    @NotNull(message = "配送批次id不能为空")
    private Long batchId;

    /**
     * 保温措施图片
     */
    @NotBlank(message = "保温措施图片不能为空")
    private String keepTemperatureMethodPics;
}
