package net.summerfarm.tms.inbound.controller.delivery.vo.city;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/9/6 14:53<br/>
 *
 * <AUTHOR> />
 */
@Data
public class LackApprovedDetailVO {

    /**
     * 状态 1 待核准 2待判责 3 已完成
     */
    private Integer state;
    /**
     * 任务编号
     */
    private Long id;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 店铺名称
     */
    private String mname;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 配送地址
     */
    private String address;
    /**
     * 城配仓
     */
    private String storeNoStr;
    /**
     * 库存仓
     */
    private String areaNoStr;
    /**
     * 配送司机
     */
    private String driverName;
    /**
     * 配送司机
     */
    private String driverPhone;
    /**
     * 配送路线编号
     */
    private String pathName;
    /**
     * 配送时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishTime;
    /**
     * 配送图片
     */
    private String deliveryPic;
    /**
     * sku
     */
    private String sku;
    /**
     * 商品名称
     */
    private String pdName;
    /**
     * 规格
     */
    private String weight;
    /**
     * 单价
     */
    private BigDecimal money;
    /**
     * 总价
     */
    private BigDecimal totalMoney;
    /**
     * 订单数量
     */
    private Integer amount;
    /**
     * 缺货数量
     */
    private Integer lackNum;
    /**
     * 缺货核准操作人
     */
    private String approvedName;

    /**
     * 核准时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approvedTime;

    /**
     * 缺货类型1.总仓-少发;2.总仓-库存不足;3.总仓-发错货;4.司机-误操作;5.司机-配送丢失;6.干线运输破损;7.其它 8总仓-腐烂
     */
    private String lackType;
    /**
     * 总仓少发数量
     */
    private Integer stockLackNum;
    /**
     * 备注
     */
    private String remark;
    /**
     * 核准照片凭证
     */
    private String pic;
    /**
     * 申诉记录
     */
    private List<AppealVO> appeals;
    /**
     * 判责信息
     */
    private LackCondemnVO lackCondemn;
    /**
     * 判责人名称
     */
    private String responsibilityName;
    /**
     * 判责时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime responsibilityTime;

    /**
     * 订单来源
     */
    private Integer orderSource;
}
