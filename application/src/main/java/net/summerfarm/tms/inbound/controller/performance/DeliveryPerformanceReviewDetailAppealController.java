package net.summerfarm.tms.inbound.controller.performance;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.converter.PageInfoConverter;
import net.summerfarm.tms.enums.TmsTrackLogEnums;
import net.summerfarm.tms.inbound.controller.performance.assembler.TmsDeliveryPerformanceReviewDetailAppealAssembler;
import net.summerfarm.tms.inbound.controller.performance.input.command.BatchCloseAppealCommandInput;
import net.summerfarm.tms.inbound.controller.performance.input.command.SubmitAppealCommandInput;
import net.summerfarm.tms.inbound.controller.performance.input.command.UpdateAppealCommandInput;
import net.summerfarm.tms.inbound.controller.performance.input.query.AppealDetailInput;
import net.summerfarm.tms.inbound.controller.performance.input.query.AppealPageQueryInput;
import net.summerfarm.tms.inbound.controller.performance.vo.AppealDetailVO;
import net.summerfarm.tms.inbound.controller.performance.vo.DeliveryPerformanceReviewDetailAppealPageVO;
import net.summerfarm.tms.local.performance.DeliveryPerformanceReviewDetailAppealQueryService;
import net.summerfarm.tms.local.performance.DeliveryPerformanceReviewDetailAppealCommandService;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealItemEntity;
import net.summerfarm.tms.performance.entity.dataobject.ReviewDetailAppealPageObj;
import net.summerfarm.tms.performance.param.query.TmsDeliveryPerformanceReviewDetailAppealItemQueryParam;
import net.summerfarm.tms.performance.repository.DeliveryPerformanceReviewDetailAppealItemQueryRepository;
import net.summerfarm.tms.performance.repository.DeliveryPerformanceReviewDetailAppealQueryRepository;
import net.summerfarm.tms.track.entity.TmsTrackLogEntity;
import net.summerfarm.tms.track.param.query.TmsTrackLogQueryParam;
import net.summerfarm.tms.track.repository.TmsTrackLogQueryRepository;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;


/**
 * @Title 履约审核申诉
 * @Description 履约审核申诉功能模块
 * <AUTHOR>
 * @date 2024-08-30 14:11:06
 * @version 1.0
 */
@RestController
@RequestMapping(value="/tms-new/performance-appeal")
public class DeliveryPerformanceReviewDetailAppealController {

	@Autowired
	private DeliveryPerformanceReviewDetailAppealCommandService deliveryPerformanceReviewDetailAppealCommandService;
	@Autowired
	private DeliveryPerformanceReviewDetailAppealQueryService deliveryPerformanceReviewDetailAppealQueryService;

	@Resource
	private DeliveryPerformanceReviewDetailAppealQueryRepository deliveryPerformanceReviewDetailAppealQueryRepository;

	@Resource
	private DeliveryPerformanceReviewDetailAppealItemQueryRepository deliveryPerformanceReviewDetailAppealItemQueryRepository;

	@Resource
	private TmsTrackLogQueryRepository tmsTrackLogQueryRepository;


	/**
	 * 履约审核申诉列表
	 * @return TmsDeliveryPerformanceReviewDetailAppealVO
	 */
	@PostMapping(value="/query/page")
	public CommonResult<PageInfo<DeliveryPerformanceReviewDetailAppealPageVO>> queryPage(@RequestBody @Validated AppealPageQueryInput input){
		PageInfo<DeliveryPerformanceReviewDetailAppealPageVO> result = PageInfoConverter.toPageResp(deliveryPerformanceReviewDetailAppealQueryService.queryReviewDetailAppealPage(input),
				TmsDeliveryPerformanceReviewDetailAppealAssembler::toDeliveryPerformanceReviewDetailAppealPageVO);
		return CommonResult.ok(result);
	}

	/**
	* 获取详情
	* @return TmsDeliveryPerformanceReviewDetailAppealVO
	*/
	@PostMapping(value = "/query/detail")
	public CommonResult<AppealDetailVO> detail(@RequestBody @Validated AppealDetailInput input){
		// 基本信息
		ReviewDetailAppealPageObj detailAppealPageObj = deliveryPerformanceReviewDetailAppealQueryRepository.queryDetailById(input.getId());
		if(detailAppealPageObj == null){
			return CommonResult.ok(null);
		}

		// 申诉信息
		List<DeliveryPerformanceReviewDetailAppealItemEntity> appealItemEntityList = deliveryPerformanceReviewDetailAppealItemQueryRepository.selectByCondition(TmsDeliveryPerformanceReviewDetailAppealItemQueryParam.builder()
				.appealId(input.getId())
				.build());

		// 轨迹信息
		List<TmsTrackLogEntity> tmsTrackLogEntities = tmsTrackLogQueryRepository.selectByCondition(TmsTrackLogQueryParam.builder()
				.bizType(TmsTrackLogEnums.BizType.APPEAL.getValue())
				.bizNo(input.getId()).build());
		if(!CollectionUtils.isEmpty(tmsTrackLogEntities)){
			tmsTrackLogEntities.sort(Comparator.comparing(TmsTrackLogEntity::getId).reversed());
		}
		// 组装信息
		return CommonResult.ok(TmsDeliveryPerformanceReviewDetailAppealAssembler.toAppealDetailVO(detailAppealPageObj, appealItemEntityList, tmsTrackLogEntities));
	}

	/**
	 * 提交申诉
	 */
	@PostMapping(value = "/upsert/submit-appeal")
	@RequiresPermissions(value = {"appeal:submit-appeal"},logical = Logical.OR)
	public CommonResult<Void> submitAppeal(@RequestBody @Validated SubmitAppealCommandInput input){
		deliveryPerformanceReviewDetailAppealCommandService.submitAppeal(input);
		return CommonResult.ok();
	}

	/**
	 * 更新申诉
	 */
	@PostMapping(value = "/upsert/update-appeal")
	@RequiresPermissions(value = {"appeal:update-appeal"},logical = Logical.OR)
	public CommonResult<Void> updateAppeal(@RequestBody @Validated UpdateAppealCommandInput input){
		deliveryPerformanceReviewDetailAppealCommandService.updateAppealResult(input);
		return CommonResult.ok();
	}

	/**
	 * 批量关闭申诉
	 */
	@PostMapping(value = "/upsert/batch-close-appeal")
	@RequiresPermissions(value = {"appeal:batch-close-appeal"},logical = Logical.OR)
	public CommonResult<Void> batchCloseAppeal(@RequestBody @Validated BatchCloseAppealCommandInput input){
		deliveryPerformanceReviewDetailAppealCommandService.batchCloseAppeal(input);
		return CommonResult.ok();
	}
}

