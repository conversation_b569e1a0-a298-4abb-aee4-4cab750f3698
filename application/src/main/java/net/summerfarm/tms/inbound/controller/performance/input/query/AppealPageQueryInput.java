package net.summerfarm.tms.inbound.controller.performance.input.query;

import lombok.Data;
import net.summerfarm.tms.base.AbstractPageQuery;
import net.xianmu.common.input.BasePageInput;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-08-30 14:11:06
 * @version 1.0
 *
 */
@Data
public class AppealPageQueryInput extends AbstractPageQuery implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 履约审核任务ID
	 */
	private Long performanceReviewTaskId;

	/**
	 * 履约审核详情ID
	 */
	private Long delPerfDetailReviewId;

	/**
	 * 司机Id
	 */
	private String driverId;

	/**
	 * 状态0待申诉 1申诉中 2申诉失败 3申诉成功 4已关闭
	 */
	private Integer status;

	/**
	 * 城配仓编号
	 */
	private Integer storeNo;

	/**
	 * 开始配送时间
	 */
	private LocalDate beginDeliveryTime;

	/**
	 * 结束配送时间
	 */
	private LocalDate endDeliveryTime;

	/**
	 * 开始创建时间
	 */
	private LocalDate beginCreateTime;

	/**
	 * 结束创建时间
	 */
	private LocalDate endCreateTime;

	/**
	 * 门店名称
	 */
	private String clientName;

}