package net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.query;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * Description: 配送单打印<br/>
 * date: 2025/1/17 11:04<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryNotePrintInput {

    /**
     * 配送日期
     */
    @NotNull(message = "配送日期不能为空")
    private LocalDate deliveryTime;

    /**
     * 城配仓编号
     */
    @NotNull(message = "城配仓编号不能为空")
    private Integer storeNo;

    /**
     * 打印方向
     * true:纵向 false:横向
     */
    @NotNull(message = "打印方向不能为空")
    private Boolean printDirection;
}
