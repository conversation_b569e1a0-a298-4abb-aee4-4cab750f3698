package net.summerfarm.tms.inbound.controller.delivery;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.aspect.TmsDataPermission;
import net.summerfarm.tms.delivery.LackApprovedService;
import net.summerfarm.tms.delivery.dto.lackapproved.*;
import net.summerfarm.tms.delivery.dto.LackApprovedCommand;
import net.summerfarm.tms.enums.LackApprovedTypeEnum;
import net.summerfarm.tms.inbound.converter.delivery.city.LackApprovedConverter;
import net.summerfarm.tms.inbound.controller.delivery.vo.city.LackApprovedDetailVO;
import net.summerfarm.tms.inbound.controller.delivery.vo.city.LackApprovedPageVO;
import net.summerfarm.tms.inbound.controller.delivery.vo.city.LackTypeVO;
import net.summerfarm.tms.query.delivery.LackApprovedQuery;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 缺货核准
 */
@RestController
@RequestMapping("/tms-new/lack-approved")
@Slf4j
public class LackApprovedController {

    @Resource
    LackApprovedService lackApprovedService;
    /**
     * 查询缺货核准列表
     * @param lackApprovedQuery 查询请求参数
     */
    @PostMapping("/lackApproved/query")
    @TmsDataPermission
    public TmsResult<PageInfo<LackApprovedPageVO>> queryLackApproved(@RequestBody @Validated LackApprovedQuery lackApprovedQuery) {
        return TmsResult.success(LackApprovedConverter.dto2VOPage(lackApprovedService.queryPage(lackApprovedQuery)));
    }

    /**
     * 查看缺货核准详情
     * @param id 缺货核准记录id
     */
    @PostMapping("/lackApproved/detail")
    public TmsResult<LackApprovedDetailVO> getDetail(@RequestParam Long id) {
        return TmsResult.success(LackApprovedConverter.approvedDto2DetailVO(lackApprovedService.getDetail(id)));
    }

    /**
     * 缺货核准导出
     * @param lackApprovedQuery 查询请求参数
     */
    @Deprecated
    @GetMapping("/lackApproved/export")
    public void exportExcel(LackApprovedQuery lackApprovedQuery, HttpServletResponse response) throws IOException {
        lackApprovedService.exportExcel(lackApprovedQuery, response);
    }

    /**
     * 缺货核准导出-接入下载中心
     * @param lackApprovedQuery 查询请求参数
     */
    @PostMapping("/export-async/approved-record")
    public TmsResult<Long> downloadApprovedRecord(@RequestBody LackApprovedQuery lackApprovedQuery){
        return TmsResult.success(lackApprovedService.downloadApprovedRecord(lackApprovedQuery));
    }

    /**
     * 缺货核准
     */
    @PostMapping("/upsert/lack-approved")
    @RequiresPermissions(value = {"lack:lack-approved"},logical = Logical.OR)
    public TmsResult<Void> lackApproved(@RequestBody @Validated LackApprovedCommand lackApprovedCommand) {
        return lackApprovedService.lackApproved(LackApprovedConverter.lackApprovedCommand2DTO(lackApprovedCommand));
    }

    /**
     * 缺货判责
     */
    @PostMapping("/upsert/lack-to-condemn")
    @RequiresPermissions(value = {"lack:lack-to-condemn"},logical = Logical.OR)
    public TmsResult<Void> lackToCondemn(@RequestBody @Validated LackToCondemnCommand lackToCondemnCommand) {
       return lackApprovedService.lackToCondemn(LackApprovedConverter.lackToCondemnCommand2DTO(lackToCondemnCommand));
    }

    /**
     * 批量判责
     * {"buyOut":1,"ids":[6567,6554,6539,6538,6522,6519,6506,6504,6488,6486,6465],"judgmentOpinion":"干线运输破损","responsible":3}
     */
    @PostMapping("/upsert/batch-lack-to-condemn")
    @RequiresPermissions(value = {"lack:batch-lack-to-condemn"},logical = Logical.OR)
    public TmsResult<Void> batchLackToCondemn(@RequestBody @Validated BatchLackToCondemnCommand batchLackToCondemnCommand) {
        return lackApprovedService.batchLackToCondemn(LackApprovedConverter.batchLackToCondemnCommand2DTO(batchLackToCondemnCommand));
    }

    /**
     * 查询缺货类型
     */
    @PostMapping("/query/lack-type")
    public TmsResult<List<LackTypeVO>> queryLackType() {
        List<LackTypeVO> list = new ArrayList<>();
        for (LackApprovedTypeEnum value : LackApprovedTypeEnum.values()) {
            LackTypeVO lackTypeVO = new LackTypeVO();
            lackTypeVO.setLackTypeCode(value.getCode());
            lackTypeVO.setLackTypeName(value.getDesc());
            list.add(lackTypeVO);
        }
        return TmsResult.success(list);
    }

    /**
     * 提交申诉
     */
    @PostMapping("/upsert/appeal-save")
    @RequiresPermissions(value = {"lack:appeal-save"},logical = Logical.OR)
    public TmsResult<Void> appealSave(@RequestBody @Validated AppealSaveCommand appealSaveCommand) {
        lackApprovedService.appealSave(appealSaveCommand);
        return TmsResult.VOID_SUCCESS;
    }

    /**
     * 查询售后补发单
     */
    @PostMapping("/query/after-sale-info")
    public TmsResult<List<AfterSaleInfoVO>> queryAfterSaleInfo(@RequestParam Long id) {
        return TmsResult.success(lackApprovedService.queryAfterSaleInfo(id));
    }

    /**
     * 历史数据初始化
     */
    @PostMapping("/upsert/init")
    public TmsResult<Void> init() {
        lackApprovedService.init();
        return TmsResult.VOID_SUCCESS;
    }

}
