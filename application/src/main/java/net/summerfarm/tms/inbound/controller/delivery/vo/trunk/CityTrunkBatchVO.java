package net.summerfarm.tms.inbound.controller.delivery.vo.trunk;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/5/5 17:24<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CityTrunkBatchVO {
    /**
     * 批次id
     */
    private Long batchId;
    /**
     * 路线名称
     */
    private String pathName;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 司机电话
     */
    private String phone;

    /**
     * 司机车牌号
     */
    private String carNumber;

    /**
     * 存储条件
     */
    private String carStorage;

    /**
     * 车型
     */
    private String carType;

    /**
     * 预计到达时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planArriveTime;

}
