package net.summerfarm.tms.inbound.controller.delivery;

import net.summerfarm.tms.inbound.controller.delivery.input.command.RouteReassignmentDriverCommandInput;
import net.summerfarm.tms.inbound.controller.delivery.input.command.SiteReassignmentRouteCommand;
import net.summerfarm.tms.service.changeDelivery.CityChangeDeliveryService;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 城配排线
 * date: 2025/4/25 15:17<br/>
 *
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/tms-new/city-delivery-arrange-route")
public class CityDeliveryArrangeRouteController {


    @Resource
    private CityChangeDeliveryService cityChangeDeliveryService;

    /**
     * 路线司机改派
     */
    @PostMapping("/upsert/route-reassignment-driver")
    public CommonResult<Void> routeReassignmentDriver(@RequestBody @Validated RouteReassignmentDriverCommandInput input) {
        cityChangeDeliveryService.routeReassignmentDriver(input);
        return CommonResult.ok();
    }

    /**
     * 未排线店铺改派
     */
    @PostMapping("/upsert/site-reassignment-route")
    public CommonResult<Void> siteReassignmentRoute(@RequestBody @Validated SiteReassignmentRouteCommand input) {
        cityChangeDeliveryService.siteReassignmentRoute(input);
        return CommonResult.ok();
    }

}
