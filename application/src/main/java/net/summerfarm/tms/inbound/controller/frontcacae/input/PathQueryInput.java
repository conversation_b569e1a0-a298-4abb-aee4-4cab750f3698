package net.summerfarm.tms.inbound.controller.frontcacae.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * Description: 排线查询缓存请求<br/>
 * date: 2024/8/12 17:31<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PathQueryInput {

    /**
     * 起点
     */
    @NotBlank(message = "起点不能为空")
    private String origin;

    /**
     * 终点
     */
    @NotBlank(message = "终点不能为空")
    private String destination;

    /**
     * 途经点
     */
    private List<String> waypoints;

    /**
     * 城配仓编号
     */
    private Integer storeNo;
}
